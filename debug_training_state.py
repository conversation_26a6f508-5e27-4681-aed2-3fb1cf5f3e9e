#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练状态调试工具
用于检查DFM训练完成后的状态管理问题
"""

import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def check_training_state():
    """检查当前的训练状态"""
    try:
        # 导入状态管理
        from dashboard.state_management.refactor import get_global_dfm_refactor
        
        # 获取状态管理器
        state_manager = get_global_dfm_refactor()
        
        print("🔍 [状态检查] DFM训练状态诊断")
        print("=" * 50)
        
        # 检查关键状态
        training_status = state_manager.get_state('dfm_training_status')
        training_results = state_manager.get_state('dfm_model_results_paths')
        ui_refresh_needed = state_manager.get_state('ui_refresh_needed')
        training_completion_timestamp = state_manager.get_state('training_completion_timestamp')
        last_processed_timestamp = state_manager.get_state('last_processed_completion_timestamp')
        
        print(f"训练状态: {training_status}")
        print(f"结果文件: {training_results}")
        print(f"UI刷新标志: {ui_refresh_needed}")
        print(f"完成时间戳: {training_completion_timestamp}")
        print(f"最后处理时间戳: {last_processed_timestamp}")
        
        # 检查结果文件详情
        if training_results:
            print(f"\n📁 结果文件详情:")
            if isinstance(training_results, dict):
                for key, path in training_results.items():
                    file_exists = os.path.exists(path) if path else False
                    print(f"  {key}: {path} (存在: {file_exists})")
            elif isinstance(training_results, list):
                for i, path in enumerate(training_results):
                    file_exists = os.path.exists(path) if path else False
                    print(f"  文件{i+1}: {path} (存在: {file_exists})")
        
        # 检查session_state（如果可用）
        try:
            import streamlit as st
            if hasattr(st, 'session_state'):
                session_training_status = st.session_state.get('dfm_training_status')
                session_training_results = st.session_state.get('dfm_model_results_paths')
                print(f"\n🔄 Session State:")
                print(f"  训练状态: {session_training_status}")
                print(f"  结果文件: {session_training_results}")
        except:
            print("\n🔄 Session State: 不可用 (非Streamlit环境)")
        
        # 状态一致性检查
        print(f"\n✅ 状态一致性检查:")
        if training_status == '训练完成' and training_results:
            print("  ✅ 训练状态和结果文件都正常")
        elif training_status == '训练完成' and not training_results:
            print("  ❌ 训练状态为完成但缺少结果文件")
        elif training_status != '训练完成' and training_results:
            print("  ❌ 有结果文件但训练状态不是完成")
        else:
            print("  ℹ️ 训练尚未完成或已重置")
        
        return {
            'training_status': training_status,
            'training_results': training_results,
            'ui_refresh_needed': ui_refresh_needed,
            'training_completion_timestamp': training_completion_timestamp
        }
        
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def fix_training_state():
    """尝试修复训练状态"""
    try:
        from dashboard.state_management.refactor import get_global_dfm_refactor
        
        state_manager = get_global_dfm_refactor()
        
        print("🔧 [状态修复] 尝试修复训练状态")
        
        # 检查是否有结果文件但状态不正确
        training_results = state_manager.get_state('dfm_model_results_paths')
        training_status = state_manager.get_state('dfm_training_status')
        
        if training_results and training_status != '训练完成':
            print(f"🔧 修复状态: {training_status} -> 训练完成")
            state_manager.set_state('dfm_training_status', '训练完成')
            
        # 清除UI刷新标志
        state_manager.set_state('ui_refresh_needed', False)
        
        print("✅ 状态修复完成")
        
    except Exception as e:
        print(f"❌ 状态修复失败: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='DFM训练状态调试工具')
    parser.add_argument('--check', action='store_true', help='检查当前状态')
    parser.add_argument('--fix', action='store_true', help='尝试修复状态')
    
    args = parser.parse_args()
    
    if args.check or not (args.check or args.fix):
        check_training_state()
    
    if args.fix:
        fix_training_state()
