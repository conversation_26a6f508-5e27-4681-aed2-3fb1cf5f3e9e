# -*- coding: utf-8 -*-
"""
经济运行分析平台 - 主dashboard
优化版本：使用统一初始化器和懒加载机制
"""

import sys
import os
import time
import warnings
import logging

# 导入Dashboard状态管理器
try:
    from core.dashboard_state_manager import get_dashboard_state_manager
except ImportError:
    try:
        from dashboard.core.dashboard_state_manager import get_dashboard_state_manager
    except ImportError:
        # 如果导入失败，抛出错误而不是使用fallback
        raise ImportError("Dashboard状态管理器导入失败，系统无法正常运行")

# 在任何其他导入之前立即抑制 Streamlit 警告
def _suppress_streamlit_warnings():
    """在模块导入前抑制 Streamlit 警告"""
    # 设置环境变量 - 包括日志级别优化
    os.environ.update({
        'STREAMLIT_LOGGER_LEVEL': 'CRITICAL',
        'STREAMLIT_CLIENT_TOOLBAR_MODE': 'minimal',
        'STREAMLIT_BROWSER_GATHER_USAGE_STATS': 'false',
        'STREAMLIT_CLIENT_SHOW_ERROR_DETAILS': 'false',
        'PYTHONWARNINGS': 'ignore',
        'STREAMLIT_SILENT_IMPORTS': 'true',
        'STREAMLIT_SUPPRESS_WARNINGS': 'true',
        # 优化日志输出级别 - 减少冗余日志
        'LOG_LEVEL': 'WARNING',  # 设置为WARNING级别减少DEBUG/INFO日志
        'ENVIRONMENT': 'production'  # 设置为生产环境模式
    })

    # 抑制所有警告
    warnings.filterwarnings("ignore")

    # 预先配置 Streamlit 日志器
    streamlit_loggers = [
        "streamlit",
        "streamlit.runtime",
        "streamlit.runtime.scriptrunner_utils",
        "streamlit.runtime.scriptrunner_utils.script_run_context",
        "streamlit.runtime.caching",
        "streamlit.runtime.caching.cache_data_api",
        "streamlit.runtime.state",
        "streamlit.runtime.state.session_state_proxy",
        "streamlit.web",
        "streamlit.web.server",
        "streamlit.web.bootstrap"
    ]

    for logger_name in streamlit_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.CRITICAL)
        logger.disabled = True
        logger.propagate = False

# 立即执行警告抑制
_suppress_streamlit_warnings()

# 添加项目根目录到Python路径，确保能正确导入dashboard包
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)  # 上一级目录
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 立即导入streamlit并设置页面配置 - 必须在任何其他操作之前
import streamlit as st

# 直接设置页面配置，避免导入问题
try:
    # 检查是否已经设置过页面配置
    if 'dashboard_page_config_set' not in st.session_state:
        st.set_page_config(
            page_title="经济运行分析平台",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        st.session_state['dashboard_page_config_set'] = True
except Exception as e:
    # 如果已经设置过，忽略错误
    pass

# 全局CSS注入标志 - 使用进程级别缓存
_css_injection_cache = {}

# CSS注入函数 - 修改为每次都注入以确保样式一致性
def inject_styles_always():
    """每次页面渲染都注入CSS - 修复样式变化问题"""
    try:
        from ui.utils.style_loader import inject_cached_styles
        # print("[DEBUG CSS] 强制重新注入CSS以确保样式一致性...")  # 移除调试输出
        inject_cached_styles()
        # print("[DEBUG CSS] CSS重新注入完成")  # 移除调试输出
    except ImportError:
        # 如果导入失败，跳过样式注入
        pass

# 保留原有函数作为备用
def inject_styles_once():
    """只执行一次CSS注入 - 使用统一状态管理"""
    # os模块已在顶部导入
    process_id = os.getpid()
    css_cache_key = f"css_injected_{process_id}"

    # 检查进程级别缓存
    if _css_injection_cache.get(css_cache_key, False):
        return

    # 尝试使用统一状态管理器
    def _inject_with_state_manager():
        try:
            from dashboard.state_management import get_unified_manager
            unified_manager = get_unified_manager()
            if unified_manager:
                dashboard_state = get_dashboard_state_manager(unified_manager)
                if dashboard_state and not dashboard_state.get_css_injection_state(css_cache_key):
                    # 执行CSS注入
                    try:
                        from ui.utils.style_loader import inject_cached_styles
                        # print("[DEBUG CSS] Starting CSS injection...")  # 移除调试输出
                        inject_cached_styles()
                        # print("[DEBUG CSS] CSS injection completed")  # 移除调试输出
                    except ImportError:
                        # 如果导入失败，跳过样式注入
                        pass

                    # 使用统一状态管理器记录状态
                    dashboard_state.set_css_injection_state(css_cache_key, True)
                    _css_injection_cache[css_cache_key] = True
                    return True
                elif dashboard_state and dashboard_state.get_css_injection_state(css_cache_key):
                    # 已经注入过，设置进程级别缓存
                    _css_injection_cache[css_cache_key] = True
                    return True
            return False
        except Exception:
            # print(f"[DEBUG CSS] 状态管理器CSS注入失败: {e}")  # 移除调试输出
            return False

    # 尝试使用状态管理器
    if not _inject_with_state_manager():
        # 如果状态管理器不可用，使用基础样式注入
        try:
            from ui.utils.style_loader import inject_cached_styles
            inject_cached_styles()
        except Exception:
            pass  # 静默处理样式注入失败

# 基础库导入
import pandas as pd
import re
import altair as alt
from datetime import datetime
import inspect
import traceback as tb

# 应用初始化将在页面配置后进行
# 延迟导入避免触发 @st.cache_resource

# === 导入UI调试工具 ===
try:
    from ui.utils.debug_helpers import (
        debug_log, debug_state_change, debug_navigation, debug_button_click
    )
except ImportError:
    # 如果导入失败，使用简单的替代函数
    def debug_log(message, level="INFO"):
        print(f"[{level}] {message}")

    def debug_state_change(key, old_value, new_value):
        print(f"[STATE] {key}: {old_value} -> {new_value}")

    def debug_navigation(action, details):
        print(f"[NAV] {action}: {details}")

    def debug_button_click(button_name, context):
        print(f"[BUTTON] {button_name}: {context}")



# 简化初始化，避免循环导入
lazy_loader = None
state_manager = None
nav_manager = None

# 第二阶段优化：导入懒加载组件
def initialize_lazy_loading():
    """初始化懒加载系统"""
    try:
        from core.component_loader import get_component_loader, preload_components_async
        from core.config_cache import get_config_cache

        # 获取组件加载器
        component_loader = get_component_loader()
        config_cache = get_config_cache()

        # 启动异步预加载
        preload_thread = preload_components_async()

        return component_loader, config_cache, preload_thread

    except Exception as e:
        return None, None, None

# 使用UI模块的导航状态同步
try:
    from ui.utils.navigation_manager import sync_navigation_state
except ImportError:
    # 如果导入失败，使用简单的替代函数
    def sync_navigation_state():
        """简单的导航状态同步替代函数"""
        pass

# 延迟初始化函数
def get_managers():
    """延迟获取管理器实例 - 优化版本，使用session_state缓存"""
    # streamlit, time, os模块已在顶部导入

    global lazy_loader, state_manager, nav_manager

    # 获取当前进程ID用于缓存键
    process_id = os.getpid()
    managers_cache_key = f"cached_managers_{process_id}"
    managers_cache_time_key = f"managers_cache_time_{process_id}"
    managers_health_key = f"managers_health_{process_id}"

    # 添加初始化锁，防止重复初始化
    init_lock_key = f"managers_init_lock_{process_id}"

    # 尝试使用统一状态管理器检查初始化锁
    def _check_init_lock_with_state_manager():
        try:
            from dashboard.state_management import get_unified_manager
            unified_manager = get_unified_manager()
            if unified_manager:
                dashboard_state = get_dashboard_state_manager(unified_manager)
                if dashboard_state:
                    # 检查初始化锁
                    if dashboard_state.get_init_lock_state(init_lock_key):
                        # 正在初始化，返回缓存的实例（如果有）
                        cached_managers = dashboard_state.get_managers_cache()
                        if cached_managers:
                            return cached_managers
                        return None, None, None
                    return False  # 没有锁
            return None  # 状态管理器不可用
        except Exception:
            return None

    # 检查初始化锁
    lock_check = _check_init_lock_with_state_manager()
    if lock_check is not None and lock_check is not False:
        return lock_check
    elif lock_check is None:
        # 如果状态管理器不可用，继续初始化流程
        # print(f"[DEBUG Init] 统一状态管理器不可用，使用简化初始化流程")  # 移除调试输出
        pass

    # 检查真正的单例缓存 - 优先使用统一状态管理器
    current_time = time.time()

    def _check_cache_with_state_manager():
        try:
            from dashboard.state_management import get_unified_manager
            unified_manager = get_unified_manager()
            if unified_manager and unified_manager.isInitialized():
                dashboard_state = get_dashboard_state_manager(unified_manager)
                if dashboard_state and dashboard_state.is_healthy():
                    cached_managers = dashboard_state.get_managers_cache()
                    cached_time = dashboard_state.get_managers_cache_time()
                    cached_health = dashboard_state.get_managers_health_status()

                    # 如果缓存在30分钟内且健康检查通过，使用缓存
                    if (cached_managers and cached_time and
                        current_time - cached_time < 1800 and cached_health):
                        # print(f"[DEBUG Init] 使用统一状态管理器缓存的管理器实例 (PID: {process_id}, 缓存时间: {current_time - cached_time:.1f}秒前)")  # 移除调试输出
                        return cached_managers
                    else:
                        # print(f"[DEBUG Init] 缓存无效或过期 (PID: {process_id})")  # 移除调试输出
                        pass
                else:
                    # print(f"[DEBUG Init] Dashboard状态管理器不可用 (PID: {process_id})")  # 移除调试输出
                    pass
            else:
                # print(f"[DEBUG Init] 统一状态管理器未初始化 (PID: {process_id})")  # 移除调试输出
                pass
            return None
        except Exception:
            # print(f"[DEBUG Init] 统一状态管理器缓存检查失败: {e}")  # 移除调试输出
            return None

    # 尝试使用统一状态管理器缓存
    cached_result = _check_cache_with_state_manager()
    if cached_result:
        lazy_loader, state_manager, nav_manager = cached_result
        # 执行状态同步
        sync_navigation_state(state_manager, nav_manager)
        return lazy_loader, state_manager, nav_manager

    # 如果统一状态管理器缓存不可用，进行首次初始化
    # print(f"[DEBUG Init] 首次初始化管理器 (PID: {process_id})")  # 移除调试输出

    # 继续执行初始化流程

    # 记录初始化开始时间
    start_time = time.time()

    # 重新初始化管理器
    if state_manager is None:
        try:
            # 正确的导入路径
            from dashboard.state_management import get_unified_manager
            debug_log("正在尝试获取统一状态管理器...", "DEBUG")
            state_manager = get_unified_manager()
            if state_manager is None:
                debug_log("统一状态管理器初始化返回None", "ERROR")
            else:
                debug_log(f"统一状态管理器初始化成功: {type(state_manager)}", "INFO")
        except Exception as e:
            print(f"[ERROR] 统一状态管理器导入失败: {e}")
            import traceback
            print(f"[ERROR] 详细错误信息: {traceback.format_exc()}")
            state_manager = None

    # 设置初始化锁（在state_manager初始化之后）
    if state_manager:
        state_manager.set_state(f'dashboard.init_lock.{init_lock_key}', True)
        debug_log(f"初始化锁设置成功: {init_lock_key}", "DEBUG")
    else:
        debug_log(f"统一状态管理器不可用，无法设置初始化锁: {init_lock_key}", "WARNING")

    if lazy_loader is None:
        try:
            # 优先尝试相对导入（适用于从项目根目录运行）
            from core.lazy_loader import get_lazy_loader
            lazy_loader = get_lazy_loader()
        except ImportError:
            try:
                # 备用相对导入（适用于从dashboard目录运行）
                from core.lazy_loader import get_lazy_loader
                lazy_loader = get_lazy_loader()
            except ImportError:
                lazy_loader = None

    if nav_manager is None and state_manager is not None:
        # 暂时使用简化的导航管理器，绕过复杂的导入问题

        class SimpleNavigationManager:
            """简化的导航管理器 - 修复版本，统一状态键"""
            def __init__(self, state_manager):
                self.state_manager = state_manager
                # 移除初始化调试打印

            def get_current_main_module(self):
                # 使用统一的状态键，与侧边栏检查逻辑保持一致
                result = self.state_manager.get_state('navigation.main_module', '数据预览')
                # 移除频繁的调试打印，避免日志污染
                return result

            def set_current_main_module(self, module_name):
                # 清理之前模块的状态
                self._clear_module_state()

                # 同时设置两个键以保持兼容性
                success1 = self.state_manager.set_state('navigation.main_module', module_name)
                success2 = self.state_manager.set_state('current_main_module', module_name)

                # 强制同步导航状态，清除所有缓存
                try:
                    from ui.components.content_router import force_navigation_state_sync
                    force_navigation_state_sync(self.state_manager, module_name, None)
                except Exception as e:
                    # 记录错误但不降级
                    print(f"[ERROR] 导航状态同步失败: {e}")

                # 移除频繁的调试打印
                return success1 or success2

            def get_current_sub_module(self):
                # 使用统一的状态键，与侧边栏检查逻辑保持一致
                result = self.state_manager.get_state('navigation.sub_module', None)
                # 移除频繁的调试打印，避免日志污染
                return result

            def set_current_sub_module(self, sub_module_name):
                # 同时设置两个键以保持兼容性
                success1 = self.state_manager.set_state('navigation.sub_module', sub_module_name)
                success2 = self.state_manager.set_state('current_sub_module', sub_module_name)

                # 确保导航状态一致性 - 保持主模块状态
                current_main = self.get_current_main_module()
                self.state_manager.set_state('navigation.main_module', current_main)

                # 强制同步导航状态，清除所有缓存
                try:
                    from ui.components.content_router import force_navigation_state_sync
                    force_navigation_state_sync(self.state_manager, current_main, sub_module_name)
                except:
                    # 降级到基本缓存清除
                    try:
                        from ui.utils.button_state_manager import clear_button_state_cache
                        clear_button_state_cache()
                    except:
                        pass

                return success1 or success2

            def set_transitioning(self, transitioning):
                """设置过渡状态（简化实现）"""
                return self.state_manager.set_state('navigation_transitioning', transitioning)

            def is_transitioning(self):
                """检查是否在过渡状态（简化实现）"""
                return self.state_manager.get_state('navigation_transitioning', False)

            def _clear_module_state(self):
                """清理模块切换时的状态残留"""
                try:
                    # 清理子模块状态
                    self.state_manager.set_state('navigation.sub_module', None)

                    # 清理临时状态
                    temp_keys = ['temp_selected_sub_module', 'navigate_to_sub_module']
                    for key in temp_keys:
                        if self.state_manager:
                            self.state_manager.clear_state(f'navigation.{key}')
                        else:
                            print(f"[WARNING] 统一状态管理器不可用，无法清理键: {key}")

                    # 重置调试状态缓存
                    if hasattr(self, '_last_main_module'):
                        delattr(self, '_last_main_module')
                    if hasattr(self, '_last_sub_module'):
                        delattr(self, '_last_sub_module')

                except Exception as e:
                    pass

        NavigationModuleRefactor = SimpleNavigationManager

        if NavigationModuleRefactor is not None:
            navigation_manager_key = 'dashboard.navigation_refactor'
            existing_nav_manager = state_manager.get_state(navigation_manager_key)
            if existing_nav_manager:
                nav_manager = existing_nav_manager
            else:
                nav_manager = NavigationModuleRefactor(state_manager)
                state_manager.set_state(navigation_manager_key, nav_manager)

    # 执行健康检查
    health_check = _perform_managers_health_check(state_manager, nav_manager)

    # 执行状态同步
    sync_navigation_state(state_manager, nav_manager)

    # 第二阶段优化：初始化懒加载系统
    component_loader, config_cache, preload_thread = initialize_lazy_loading()

    # 计算初始化耗时
    end_time = time.time()
    init_time = (end_time - start_time) * 1000  # 转换为毫秒

    # 缓存到统一状态管理器 (优先) 和 session_state (备用)
    managers_tuple = (lazy_loader, state_manager, nav_manager)

    # 尝试使用统一状态管理器缓存
    def _cache_with_state_manager():
        try:
            dashboard_state = get_dashboard_state_manager(state_manager)
            if dashboard_state:
                success = dashboard_state.set_managers_cache(managers_tuple, health_check)
                if success:
                
                    return True
            return False
        except Exception as e:
            print(f"[DEBUG Init] 统一状态管理器缓存失败: {e}")
            return False

    # 尝试统一状态管理器缓存
    unified_cache_success = _cache_with_state_manager()

    # 使用统一状态管理器缓存（移除session_state备用）
    if state_manager:
        state_manager.set_state('dashboard.managers_cache', managers_tuple)
        state_manager.set_state('dashboard.managers_cache_time', current_time)
        state_manager.set_state('dashboard.managers_health', health_check)

        # 缓存懒加载组件
        if component_loader:
            state_manager.set_state(f'dashboard.component_loader_{process_id}', component_loader)
        if config_cache:
            state_manager.set_state(f'dashboard.config_cache_{process_id}', config_cache)
    else:
        # 统一状态管理器不可用时，记录错误但不抛出异常
        print("[ERROR] 统一状态管理器不可用，无法缓存管理器")



    # 释放初始化锁
    if state_manager:
        state_manager.set_state(f'dashboard.init_lock.{init_lock_key}', False)
    else:
        print(f"[WARNING] 统一状态管理器不可用，无法释放初始化锁: {init_lock_key}")

    return lazy_loader, state_manager, nav_manager

def _perform_managers_health_check(state_manager, nav_manager):
    """执行管理器健康检查"""
    try:
        # 检查状态管理器
        if state_manager is not None:
            # 尝试调用一个简单的方法来验证状态管理器是否正常工作
            if hasattr(state_manager, 'get_state'):
                state_manager.get_state('health_check_test', 'default')

        # 检查导航管理器
        if nav_manager is not None:
            # 尝试调用一个简单的方法来验证导航管理器是否正常工作
            if hasattr(nav_manager, 'get_current_main_module'):
                nav_manager.get_current_main_module()

        return True
    except Exception as e:
        print(f"[DEBUG Init] 管理器健康检查失败: {e}")
        return False

# 使用UI模块的导航管理器
try:
    from ui.utils.navigation_manager import check_navigation_change
    # 为了向后兼容，保持原有的函数名
    _check_navigation_change = check_navigation_change
except ImportError:
    # 如果导入失败，使用简单的替代函数
    def _check_navigation_change():
        return False

# 使用UI模块的按钮状态管理
try:
    from ui.utils.button_state_manager import optimize_button_state_management
    # 为了向后兼容，保持原有的函数名
    _optimize_button_state_management = optimize_button_state_management
except ImportError:
    # 如果导入失败，使用简单的替代函数
    def _optimize_button_state_management():
        pass



# 配置Altair数据转换器
try:
    alt.data_transformers.enable("vegafusion")
except ImportError:
    pass

# --- Sidebar Title (moved to main sidebar section) ---
# st.sidebar.title("📈 经济运行分析平台")

# --- 状态初始化将在获取管理器实例后进行 ---

# --- 工具函数 ---
# extract_industry_name函数已移至utils模块，避免重复定义

# --- 使用UI模块的状态管理辅助函数 ---
try:
    from ui.utils.state_helpers import (
        get_dashboard_state, set_dashboard_state, get_staged_data,
        get_staged_data_options, clear_analysis_states as ui_clear_analysis_states,
        set_analysis_data, clear_analysis_data
    )
except ImportError:
    # 如果导入失败，抛出错误
    raise ImportError("状态管理函数导入失败，系统无法正常运行")

def clear_analysis_states(analysis_type: str, selected_name: str = None):
    """清理分析相关状态 - 使用UI模块的状态管理器"""
    try:
        # 尝试使用UI模块的清理函数
        if 'ui_clear_analysis_states' in globals():
            return ui_clear_analysis_states(analysis_type)

        # 如果UI模块的清理函数不可用，抛出错误
        raise RuntimeError(f"分析状态清理失败：UI模块清理函数不可用 (analysis_type: {analysis_type})")
    except Exception:
        pass  # 清理状态时出错

# set_analysis_data和clear_analysis_data函数已通过UI模块导入
# 如果导入失败，会使用上面定义的fallback版本

# --- 优化的状态管理器已在上方初始化 ---

# --- 工具函数已移至utils模块 ---
# extract_industry_name函数已移至dashboard.utils.industry_utils模块，避免重复定义

# --- 优化的样式加载（已在初始化器中处理） ---

# --- Module Configuration (NEW) ---
MODULE_CONFIG = {
    "数据预览": {
        "工业": None,
        "消费": None,
        # 可以根据需要添加更多领域，例如农业、服务业等
    },
    "监测分析": {
        "工业": ["工业增加值", "工业企业利润拆解"],
        "消费": ["宏观运行", "企业经营"]
    },
    "模型分析": {
        "DFM 模型": ["数据准备", "模型训练", "模型分析", "新闻分析"] # <<< 修改：增加"模型训练"
    },
    "应用工具": {
        "数据预处理": ["数据清洗", "变量计算", "数据追加与合并", "数据比较"], # <--- 修改此处，增加"数据比较"
        "数据探索": ["平稳性分析", "相关分析"]
    }
}


def _perform_intelligent_state_cleanup():
    """执行智能状态清理，避免循环渲染"""
    try:
        # 使用统一状态管理器清理临时状态
        if state_manager:
            # 获取所有状态键
            all_keys = state_manager.get_all_keys()

            # 清理导航相关的临时状态
            navigation_patterns = ['navigate_to', 'temp_selected', 'rerun', '_transition', '_loading']
            navigation_keys = [k for k in all_keys if any(pattern in str(k) for pattern in navigation_patterns)]

            for key in navigation_keys:
                state_manager.clear_state(key)

            # 清理可能导致循环的组件状态
            component_patterns = ['_preview_data', '_processed_data', '_analysis_result', '_cached_']
            component_keys = [k for k in all_keys if any(pattern in str(k) for pattern in component_patterns)]

            for key in component_keys:
                state_manager.clear_state(key)
        else:
            # 统一状态管理器不可用时，记录错误
            print("[ERROR] 统一状态管理器不可用，无法进行状态清理")

        print(f"[DEBUG Recovery] 清理了 {len(navigation_keys + component_keys)} 个状态键")

    except Exception as e:
        print(f"[DEBUG Recovery] 状态清理失败: {e}")


# --- 统一导航管理的侧边栏 ---
# 每次都注入CSS样式以确保样式一致性（修复模块切换时样式变化问题）
inject_styles_always()

# 获取管理器实例
lazy_loader, state_manager, nav_manager = get_managers()

# --- 渲染稳定化检查（使用统一状态管理器） ---
# time模块已在顶部导入
current_time = time.time()

# 检查是否有模块正在加载
if state_manager is not None:
    all_keys = state_manager.get_all_keys()
    loading_modules = [key for key in all_keys if key.startswith('_loading_')]
    if loading_modules:
        st.stop()

    # 检测快速连续重新渲染（回弹检测）- 改进版本
    last_render_time = state_manager.get_state('dashboard.last_render_time', 0)
    render_interval = current_time - last_render_time

    # 检查是否是用户主动导航操作导致的重新渲染
    is_navigation_triggered = False
    if nav_manager:
        # 检查导航状态是否在变化中
        is_navigation_triggered = nav_manager.is_transitioning()

        # 检查是否在导航操作的时间窗口内（2秒内的导航操作都认为是用户主动的）
        if not is_navigation_triggered and state_manager:
            last_nav_time = state_manager.get_state('dashboard.last_navigation_time', 0)
            if current_time - last_nav_time < 2.0:  # 2秒的导航操作窗口
                is_navigation_triggered = True

    # 只有在非导航触发且间隔很短的情况下才视为回弹
    if render_interval < 0.05 and last_render_time > 0 and not is_navigation_triggered:  # 50ms阈值，排除导航触发
        st.stop()

    state_manager.set_state('dashboard.last_render_time', current_time)
else:
    # 如果状态管理器不可用，跳过渲染跟踪
    pass

# 如果管理器正在初始化，跳过本次渲染
if lazy_loader is None or state_manager is None or nav_manager is None:
    st.info("系统正在初始化，请稍候...")
    st.stop()

# --- 状态初始化 ---
try:
    if state_manager is not None:
        state_manager.set_state('dashboard.initialized', True)
        state_manager.set_state('dashboard.start_time', datetime.now())
except Exception as e:
    st.error(f"状态初始化失败: {e}")
    st.stop()

# 管理器初始化结果已在get_managers()函数中打印，避免重复

# 使用统一状态管理器确保键的唯一性和防止重复渲染
if state_manager:
    if not state_manager.get_state('dashboard.sidebar.rendered', False):
        state_manager.set_state('dashboard.sidebar.rendered', True)
        state_manager.set_state('dashboard.sidebar.key_counter', 0)
        state_manager.set_state('dashboard.main_content.rendered', False)
else:
    # 降级处理：记录警告
    print("[WARNING] 统一状态管理器不可用，无法设置侧边栏渲染状态")

# 改进的循环渲染检测机制 - 使用统一状态管理
current_time = time.time()

def _manage_render_tracking_with_state_manager():
    """使用统一状态管理器管理渲染跟踪"""
    try:
        _, state_manager, _ = get_managers()
        if state_manager:
            dashboard_state = get_dashboard_state_manager(state_manager)
            if dashboard_state:
                tracking = dashboard_state.get_render_tracking()

                # 每30秒重置计数器，避免正常使用被误判
                if current_time - tracking['last_reset'] > 30:
                    dashboard_state.reset_render_tracking()
                    tracking = dashboard_state.get_render_tracking()

                # 增加渲染计数
                dashboard_state.increment_render_count()
                tracking = dashboard_state.get_render_tracking()

                # 检测短时间内的快速渲染
                render_interval = current_time - tracking['last_render']
                return tracking, render_interval
        return None, None
    except Exception as e:
        print(f"[DEBUG Render] 统一状态管理器渲染跟踪失败: {e}")
        return None, None

# 尝试使用统一状态管理器
tracking, render_interval = _manage_render_tracking_with_state_manager()

# 如果统一状态管理器不可用，跳过渲染跟踪
if tracking is None:
    pass

# 改进循环渲染检测：更严格的条件，避免误判用户正常操作
# 只有在极短时间内（<0.05秒）连续渲染超过10次且非导航触发时才认为是循环渲染
is_user_navigation = False
if nav_manager and state_manager:
    # 检查是否是用户导航操作
    is_user_navigation = (nav_manager.is_transitioning() or
                         (current_time - state_manager.get_state('dashboard.last_navigation_time', 0) < 3.0))

if (render_interval and render_interval < 0.05 and tracking and tracking['count'] > 10 and
    not is_user_navigation):
    # 只在真正的循环渲染时显示警告，避免对用户造成困扰
    st.warning("⚠️ 检测到异常的页面渲染循环，正在自动修复...")

    # 智能状态清理
    _perform_intelligent_state_cleanup()

    # 重置渲染计数 - 优先使用统一状态管理器
    def _reset_render_count():
        try:
            _, state_manager, _ = get_managers()
            if state_manager:
                dashboard_state = get_dashboard_state_manager(state_manager)
                if dashboard_state:
                    dashboard_state.reset_render_tracking()
                    return True
            return False
        except Exception:
            return False

    if not _reset_render_count():
        # 如果重置失败，跳过
        pass

    # 简化恢复信息，减少对用户的干扰
    with st.expander("🔧 系统状态", expanded=False):
        st.info("✅ 系统已自动修复渲染问题")
        st.info("💡 如果页面仍有异常，请刷新页面")
        if st.button("🔄 刷新页面", key="manual_refresh_button"):
            st.rerun()

# 🔥 修复：使用统一状态管理器而不是session_state
# 使用统一状态管理器确保key稳定性
if state_manager:
    stable_key_prefix = state_manager.get_state('dashboard.sidebar.stable_key_prefix')
    if not stable_key_prefix:
        # 使用时间戳确保唯一性
        import time
        timestamp = str(int(time.time() * 1000))[-6:]  # 使用最后6位毫秒
        stable_key_prefix = f"main_sidebar_{timestamp}"
        state_manager.set_state('dashboard.sidebar.stable_key_prefix', stable_key_prefix)
    key_prefix = stable_key_prefix
else:
    # 降级处理
    key_prefix = "main_sidebar_fallback"

# 使用UI模块的完整侧边栏组件
try:
    # 延迟导入避免循环导入
    import importlib
    sidebar_module = importlib.import_module('ui.components.sidebar')
    render_complete_sidebar = getattr(sidebar_module, 'render_complete_sidebar')

    # 在侧边栏渲染前强制同步导航状态，确保按钮状态正确
    if state_manager and nav_manager:
        try:
            current_main = nav_manager.get_current_main_module()
            current_sub = nav_manager.get_current_sub_module()

            if current_main or current_sub:
                try:
                    # 延迟导入避免循环导入
                    import importlib
                    content_router_module = importlib.import_module('ui.components.content_router')
                    force_navigation_state_sync = getattr(content_router_module, 'force_navigation_state_sync')
                    force_navigation_state_sync(state_manager, current_main, current_sub)
                except (ImportError, AttributeError):
                    pass
                # 移除侧边栏调试打印
        except Exception as e:
            print(f"[ERROR] 侧边栏渲染前状态同步失败: {e}")

    # 渲染完整侧边栏
    sidebar_result = render_complete_sidebar(MODULE_CONFIG, nav_manager, key_prefix)

    # 提取结果
    main_selector_result = sidebar_result.get('main_module_result', {})
    sub_selector_result = sidebar_result.get('sub_module_result')

    selected_main_module_val = main_selector_result.get('selected_module', '数据预览')
    change_result = main_selector_result.get('success', True)

    if sub_selector_result:
        selected_sub_module_val = sub_selector_result.get('selected_sub_module')
    else:
        selected_sub_module_val = None

    # 处理临时状态恢复
    if state_manager:
        temp_main = state_manager.get_state('navigation.temp_selected_main_module')
        if temp_main:
            selected_main_module_val = temp_main
            state_manager.clear_state('navigation.temp_selected_main_module')
            debug_navigation("临时状态恢复", f"从state_manager恢复主模块选择: {selected_main_module_val}")

        temp_sub = state_manager.get_state('navigation.temp_selected_sub_module')
        if temp_sub:
            selected_sub_module_val = temp_sub
            state_manager.set_state('navigation.last_clicked_sub_module', selected_sub_module_val)
            state_manager.clear_state('navigation.temp_selected_sub_module')
    else:
        # 统一状态管理器不可用时，记录错误
        debug_navigation("状态管理器不可用", "无法处理临时状态恢复")

    # 获取当前状态用于后续逻辑
    current_main_module = nav_manager.get_current_main_module() if nav_manager else '数据预览'
    current_sub_module = nav_manager.get_current_sub_module() if nav_manager else None

    # 立即更新当前模块变量以确保按钮状态同步
    if selected_main_module_val != current_main_module:
        debug_state_change("主模块切换", current_main_module, selected_main_module_val, "用户点击按钮")

except ImportError as e:
    # 记录导入错误但不渲染fallback，避免重复渲染
    print(f"[ERROR] Sidebar import failed: {e}")
    st.error(f"侧边栏组件导入失败: {e}")

    # 设置默认值但不渲染重复的侧边栏
    if nav_manager:
        current_main_module = nav_manager.get_current_main_module()
    else:
        current_main_module = '数据预览'

    selected_main_module_val = current_main_module
    selected_sub_module_val = None
    change_result = True

# 处理主模块切换逻辑
if selected_main_module_val != current_main_module:
    # 设置导航状态为转换中
    if nav_manager:
        nav_manager.set_transitioning(True)
        debug_navigation("转换状态设置", "设置transitioning=True")

    current_main_module = selected_main_module_val

    # 主模块切换时，清除子模块状态以避免状态污染
    debug_navigation("状态清除", "主模块切换，开始清除子模块状态")
    if nav_manager:
        nav_manager.set_current_sub_module(None)

    # 避免重复渲染，只在必要时重新运行
    # 检查是否真的需要重新渲染
    if state_manager:
        last_change = state_manager.get_state('dashboard.last_main_module_change')
        if last_change != selected_main_module_val:
            state_manager.set_state('dashboard.last_main_module_change', selected_main_module_val)
            debug_navigation("重新渲染", f"主模块切换到 {selected_main_module_val}，触发重新渲染")
    else:
        # 统一状态管理器不可用时，记录错误
        debug_navigation("状态管理器不可用", f"无法记录主模块变更: {selected_main_module_val}")
else:
    # 确保非切换时清除transitioning状态
    if nav_manager:
        nav_manager.set_transitioning(False)



# --- 主区域（使用主内容路由组件） ---
# 使用UI模块的主内容路由组件
try:
    from ui.components.content_router import render_main_content

    # 渲染主内容
    content_result = render_main_content(nav_manager)

    debug_navigation(
        "主内容渲染",
        f"内容渲染完成 - 模块: {content_result.get('main_module')}, "
        f"子模块: {content_result.get('sub_module')}, "
        f"状态: {content_result.get('status')}"
    )

except ImportError:
    # 如果导入失败，抛出错误
    raise ImportError("主内容路由模块导入失败，系统无法正常运行")

# 在内容渲染完成后清除transitioning状态
if nav_manager:
    nav_manager.set_transitioning(False)

# (End of script)