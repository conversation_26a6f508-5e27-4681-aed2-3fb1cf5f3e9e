# -*- coding: utf-8 -*-
"""
DFM模型训练页面组件

完全重构版本，与dfm_old_ui/train_model_ui.py保持完全一致
"""

import streamlit as st
import pandas as pd
import numpy as np
import os
import sys
import unicodedata
from datetime import datetime, timedelta, date, time
from collections import defaultdict
import traceback
import threading
from typing import Dict, List, Optional, Union, Any

# 添加路径以导入统一状态管理
current_dir = os.path.dirname(os.path.abspath(__file__))
dashboard_root = os.path.abspath(os.path.join(current_dir, '..', '..', '..'))
if dashboard_root not in sys.path:
    sys.path.insert(0, dashboard_root)

# 导入统一状态管理
from dashboard.state_management.refactor import get_global_dfm_refactor
from dashboard.state_management.events.state_events import StateEventSystem, EventType, get_global_event_system
import logging

# 导入组件化训练状态管理
from dashboard.ui.components.dfm.train_model.training_status import TrainingStatusComponent
from dashboard.ui.components.dfm.train_model.event_manager import get_event_manager
from dashboard.ui.components.dfm.train_model.event_handlers import TrainingEventHandler

# 配置日志记录器
logger = logging.getLogger(__name__)

# 🔥 新增：创建全局组件化训练状态管理器
_training_status_component = None

def get_training_status_component():
    """获取训练状态组件实例（单例模式）"""
    global _training_status_component
    if _training_status_component is None:
        _training_status_component = TrainingStatusComponent()
    return _training_status_component

# 🔥 新增：调试和监控函数
def debug_training_state(message: str, show_in_ui: bool = False):
    """
    调试训练状态同步过程

    Args:
        message: 调试消息
        show_in_ui: 是否在UI中显示调试信息
    """
    timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
    debug_msg = f"[{timestamp}] {message}"

    # 控制台输出
    print(debug_msg)

    # 日志记录
    logger.debug(debug_msg)

    # 可选的UI显示（用于开发调试）
    if show_in_ui and hasattr(st, 'sidebar'):
        with st.sidebar.expander("🔧 训练状态调试", expanded=False):
            st.text(debug_msg)

def check_current_training_state():
    """检查当前训练状态的详细信息"""
    try:
        training_status = get_dfm_state('dfm_training_status')
        training_results = get_dfm_state('dfm_model_results_paths')
        training_completed_refreshed = get_dfm_state('training_completed_refreshed')
        polling_count = get_dfm_state('training_completion_polling_count', 0)
        training_log = get_dfm_state('dfm_training_log', [])

        debug_msg = f"""
🔍 当前训练状态检查:
- dfm_training_status: {training_status}
- dfm_model_results_paths: {training_results}
- training_completed_refreshed: {training_completed_refreshed}
- training_completion_polling_count: {polling_count}
- dfm_training_log 条数: {len(training_log)}
"""
        print(debug_msg)
        return {
            'training_status': training_status,
            'training_results': training_results,
            'training_completed_refreshed': training_completed_refreshed,
            'polling_count': polling_count,
            'log_count': len(training_log)
        }
    except Exception as e:
        print(f"❌ 状态检查失败: {e}")
        return None

# 配置已移除，所有参数通过UI设置
CONFIG_AVAILABLE = False

# 配置已移除，使用硬编码默认值
class TrainModelConfig:
    # 基于项目结构的路径设置
    PROJECT_ROOT = os.path.abspath(os.path.join(current_dir, '..', '..', '..', '..'))

    # UI默认配置值
    TYPE_MAPPING_SHEET = '指标体系'
    TARGET_VARIABLE = '规模以上工业增加值:当月同比'
    INDICATOR_COLUMN_NAME_IN_EXCEL = '高频指标'
    INDUSTRY_COLUMN_NAME_IN_EXCEL = '行业'
    TYPE_COLUMN_NAME_IN_EXCEL = '类型'

config = TrainModelConfig()

# --- Module Import Error Handling ---
_TRAIN_UI_IMPORT_ERROR_MESSAGE = None # Stores combined error messages
_DATA_PREPARATION_MODULE = None

# 2. 尝试导入数据预处理模块
try:
    # 从data_prep目录导入
    data_prep_dir = os.path.join(dashboard_root, 'dashboard', 'DFM', 'data_prep')
    if data_prep_dir not in sys.path:
        sys.path.insert(0, data_prep_dir)

    # 直接实现load_mappings函数，避免复杂的导入问题
    def load_mappings_direct(excel_path: str, sheet_name: str, indicator_col: str = '高频指标',
                           type_col: str = '类型', industry_col: str = '行业'):
        """直接实现的load_mappings函数"""
        try:
            import pandas as pd
            df = pd.read_excel(excel_path, sheet_name=sheet_name)

            var_type_map = {}
            var_industry_map = {}

            if indicator_col in df.columns:
                if type_col in df.columns:
                    var_type_map = dict(zip(df[indicator_col].fillna(''), df[type_col].fillna('')))
                if industry_col in df.columns:
                    var_industry_map = dict(zip(df[indicator_col].fillna(''), df[industry_col].fillna('')))

            return var_type_map, var_industry_map
        except Exception as e:
            print(f"load_mappings_direct错误: {e}")
            return {}, {}

    class MappingOnlyWrapper:
        @staticmethod
        def load_mappings(*args, **kwargs):
            return load_mappings_direct(*args, **kwargs)

    _DATA_PREPARATION_MODULE = MappingOnlyWrapper()
    print("✅ 数据预处理模块（直接实现）加载成功")
    # 清除任何之前的错误消息，因为导入成功了
    _TRAIN_UI_IMPORT_ERROR_MESSAGE = None

except ImportError as e_dp:
    error_msg_dp = (
        f"Failed to import data_preparation: {e_dp}. "
        "Using mock data preparation. Functionality may be limited."
    )
    if _TRAIN_UI_IMPORT_ERROR_MESSAGE:
        _TRAIN_UI_IMPORT_ERROR_MESSAGE += f"\n{error_msg_dp}"
    else:
        _TRAIN_UI_IMPORT_ERROR_MESSAGE = error_msg_dp

    class MockDataPreparation:
        @staticmethod
        def load_mappings(excel_path, sheet_name, indicator_col, type_col, industry_col):
            try:
                # 尝试直接读取Excel文件作为fallback
                if os.path.exists(excel_path):
                    df = pd.read_excel(excel_path, sheet_name=sheet_name)
                    if indicator_col in df.columns and industry_col in df.columns:
                        var_industry_map = dict(zip(df[indicator_col].fillna(''), df[industry_col].fillna('')))
                        var_type_map = {}
                        if type_col in df.columns:
                            var_type_map = dict(zip(df[indicator_col].fillna(''), df[type_col].fillna('')))
                        return var_type_map, var_industry_map
            except Exception as e:
                pass  # 静默处理错误
            return {}, {} # Return empty mappings if all fails

    _DATA_PREPARATION_MODULE = MockDataPreparation()

# Make the data_preparation (real or mock) available for the rest of the module
data_preparation = _DATA_PREPARATION_MODULE

# 3. 导入DFM训练脚本
try:
    # 添加DFM训练模块路径
    dfm_train_dir = os.path.join(dashboard_root, 'dashboard', 'DFM', 'train_model')
    if dfm_train_dir not in sys.path:
        sys.path.insert(0, dfm_train_dir)

    # 添加更多可能的路径
    possible_paths = [
        dfm_train_dir,
        os.path.join(dashboard_root, 'DFM', 'train_model'),
        os.path.join(os.path.dirname(dashboard_root), 'dashboard', 'DFM', 'train_model')
    ]

    for path in possible_paths:
        if path not in sys.path and os.path.exists(path):
            sys.path.insert(0, path)

    # 直接导入模块
    import tune_dfm

    # 检查函数是否存在
    if hasattr(tune_dfm, 'train_and_save_dfm_results'):
        train_and_save_dfm_results = tune_dfm.train_and_save_dfm_results
        print(f"✅ 成功导入train_and_save_dfm_results函数")
        # 如果tune_dfm导入成功，清除所有错误消息
        _TRAIN_UI_IMPORT_ERROR_MESSAGE = None
    else:
        raise ImportError("train_and_save_dfm_results function not found in tune_dfm module")

except ImportError as e_tune_dfm:
    error_msg_tune_dfm = (
        f"Failed to import 'tune_dfm.train_and_save_dfm_results': {e_tune_dfm}. "
        "Actual model training will not be possible."
    )
    if _TRAIN_UI_IMPORT_ERROR_MESSAGE:
        _TRAIN_UI_IMPORT_ERROR_MESSAGE += f"\n{error_msg_tune_dfm}"
    else:
        _TRAIN_UI_IMPORT_ERROR_MESSAGE = error_msg_tune_dfm
    # Define a mock function if import fails
    def train_and_save_dfm_results(*args, **kwargs):
        st.error("Mock train_and_save_dfm_results called due to import error. No training will occur.")
        # Simulate an error or empty result as training didn't happen
        raise RuntimeError("Model training function (train_and_save_dfm_results) is not available due to import error.")

# 模拟的UIDefaults和TrainDefaults类
class UIDefaults:
    NUM_COLS_INDUSTRY = 3
    VARIABLE_SELECTION_OPTIONS = {
        'none': "无筛选 (使用全部已选变量)",
        'global_backward': "全局后向剔除 (在已选变量中筛选)"
    }
    FACTOR_SELECTION_STRATEGY_OPTIONS = {
        'information_criteria': "信息准则",
        'fixed_number': "固定因子数",
        'cumulative_variance': "累积方差贡献"
    }
    INFORMATION_CRITERION_OPTIONS = {
        'bic': "BIC",
        'aic': "AIC",
        'hqc': "HQC"
    }
    EM_CONVERGENCE_CRITERION_OPTIONS = {
        'params': "参数变化",
        'likelihood': "似然函数"
    }
    MISSING_VALUE_OPTIONS = {
        'interpolate': "线性插值",
        'forward_fill': "前向填充",
        'drop': "删除缺失"
    }
    IC_MAX_FACTORS_DEFAULT = 10
    MAX_ITERATIONS_DEFAULT = 30
    MAX_ITERATIONS_MIN = 1
    MAX_ITERATIONS_STEP = 10
    CUM_VARIANCE_MIN = 0.1

class TrainDefaults:
    VARIABLE_SELECTION_METHOD = 'none'
    FACTOR_SELECTION_STRATEGY = 'information_criteria'
    EM_MAX_ITER = 100
    FIXED_NUMBER_OF_FACTORS = 3
    CUM_VARIANCE_THRESHOLD = 0.8
    FACTOR_AR_ORDER = 1
    INFORMATION_CRITERION = 'bic'
    EM_CONVERGENCE_CRITERION = 'params'
    EM_TOLERANCE = 1e-6
    MISSING_VALUE_METHOD = 'interpolate'

    IC_MAX_FACTORS = 10
    K_FACTORS_RANGE_MIN = 1
    TRAINING_YEARS_BACK = 5
    VALIDATION_END_YEAR = 2024
    VALIDATION_END_MONTH = 12
    VALIDATION_END_DAY = 31

# 全局DFM重构适配器实例
_dfm_refactor = None



# --- 下载状态管理函数 ---
def manage_download_state(action, session_id=None):
    """统一管理下载相关状态"""
    import uuid
    import time

    if action == 'start':
        # 生成新的下载会话ID
        if session_id is None:
            session_id = str(uuid.uuid4())[:8]

        # 设置下载状态
        set_dfm_state('dfm_downloading_files', True)
        set_dfm_state('dfm_download_start_time', time.time())
        set_dfm_state('dfm_download_session_id', session_id)
        return session_id

    elif action == 'check':
        # 检查下载状态
        is_downloading = get_dfm_state('dfm_downloading_files', False)
        start_time = get_dfm_state('dfm_download_start_time', 0)
        session_id = get_dfm_state('dfm_download_session_id', '')

        return {
            'is_downloading': is_downloading,
            'start_time': start_time,
            'session_id': session_id,
            'duration': time.time() - start_time if start_time else 0
        }

    elif action == 'clear':
        # 清理下载状态
        set_dfm_state('dfm_downloading_files', False)
        set_dfm_state('dfm_download_start_time', None)
        set_dfm_state('dfm_download_session_id', None)

        return True

    return False


def is_download_protected():
    """检查当前是否处于下载保护状态"""
    download_info = manage_download_state('check')

    # 检查是否正在下载
    if not download_info['is_downloading']:
        return False

    # 检查下载是否超时（5分钟）
    if download_info['duration'] > 300:
        print(f"⚠️ [下载保护] 下载会话超时，自动清理: {download_info['session_id']}")
        manage_download_state('clear')
        return False

    return True


def cleanup_expired_downloads(timeout_seconds=300):
    """清理超时的下载状态"""
    download_info = manage_download_state('check')

    if download_info['is_downloading'] and download_info['duration'] > timeout_seconds:
        print(f"🧹 [下载清理] 清理超时下载会话: {download_info['session_id']}")
        manage_download_state('clear')
        return True

    return False


def validate_date_consistency():
    """验证数据准备tab和模型训练tab的日期参数一致性"""
    data_prep_start = get_dfm_state('dfm_param_data_start_date')
    data_prep_end = get_dfm_state('dfm_param_data_end_date')
    training_start = get_dfm_state('dfm_training_start_date')
    validation_end = get_dfm_state('dfm_validation_end_date')

    # 如果数据准备页面设置了边界，确保训练参数在合理范围内
    if data_prep_start and training_start:
        if training_start < data_prep_start:
            print(f"⚠️ [日期一致性] 训练开始日期 {training_start} 早于数据边界 {data_prep_start}")

    if data_prep_end and validation_end:
        if validation_end > data_prep_end:
            print(f"⚠️ [日期一致性] 验证结束日期 {validation_end} 晚于数据边界 {data_prep_end}")

    return True


def load_mappings_from_unified_state(available_data_columns=None):
    """
    从统一状态管理器中获取映射数据构建行业到指标的映射
    优先使用数据准备模块已经加载好的映射，避免重复读取Excel文件

    Args:
        available_data_columns: 实际数据中可用的列名列表（用于过滤）

    Returns:
        tuple: (unique_industries, industry_to_indicators_map, all_indicators_flat)
               如果没有找到映射数据，返回 (None, None, None)
    """
    try:
        # 🔥 修复：使用正确的键名获取映射数据
        var_industry_map = get_dfm_state('dfm_industry_map_obj', None)

        if var_industry_map is None:
            print("⚠️ [映射加载] 统一状态管理器中未找到行业映射数据")
            return None, None, None

        if not var_industry_map:
            print("⚠️ [映射加载] 行业映射数据为空")
            return None, None, None

        # 如果提供了实际数据列名，进行过滤
        if available_data_columns is not None:
            # 标准化实际数据的列名
            normalized_data_columns = {}
            for col in available_data_columns:
                if col and pd.notna(col):
                    norm_col = unicodedata.normalize('NFKC', str(col)).strip().lower()
                    if norm_col:
                        normalized_data_columns[norm_col] = col

            # 过滤映射，只保留实际存在的变量
            filtered_var_industry_map = {}
            for indicator_norm, industry in var_industry_map.items():
                if indicator_norm in normalized_data_columns:
                    filtered_var_industry_map[indicator_norm] = industry

            var_industry_map = filtered_var_industry_map

            # 添加缓存机制避免重复打印
            original_count = len(get_dfm_state('dfm_industry_map_obj', {}))
            filtered_count = len(var_industry_map)
            cache_key = f"mapping_filter_log_{original_count}_{filtered_count}"

            if not get_dfm_state(cache_key, False):
                print(f"📊 [映射过滤] 原始映射 {original_count} 个变量，过滤后 {filtered_count} 个变量")
                set_dfm_state(cache_key, True)

        # 构建行业到指标的映射
        industry_to_indicators_temp = defaultdict(list)
        for indicator, industry in var_industry_map.items():
            if indicator and industry:
                industry_to_indicators_temp[str(industry).strip()].append(str(indicator).strip())

        # 排序并返回结果
        unique_industries = sorted(list(industry_to_indicators_temp.keys()))
        industry_to_indicators_map = {k: sorted(v) for k, v in industry_to_indicators_temp.items()}
        all_indicators_flat = sorted(list(var_industry_map.keys()))

        return unique_industries, industry_to_indicators_map, all_indicators_flat

    except Exception as e:
        print(f"❌ [映射加载] 从统一状态管理器加载映射数据失败: {e}")
        return None, None, None


# --- 新增：状态重置检查函数 ---


def _reset_training_state():
    """重置所有训练相关状态（使用组件化方法）"""
    try:
        # 🔥 修复：使用组件化方法重置训练状态
        training_component = get_training_status_component()
        training_component._reset_training_state()

        from dashboard.ui.utils.debug_helpers import debug_log
        debug_log("状态重置 - 使用组件化方法重置训练状态", "DEBUG")

        # 🔥 关键修复：直接清理session_state中的训练相关状态
        import streamlit as st
        training_keys = [
            'dfm_training_status',
            'dfm_training_log',
            'dfm_training_progress',
            'dfm_model_results_paths',
            'dfm_model_results',
            'dfm_training_error',
            'dfm_training_start_time',
            'dfm_training_end_time',
            'training_completed_refreshed',
            'training_completion_polling_count',
            'dfm_force_reset_training_state',
            'dfm_page_initialized'
        ]

        for key in training_keys:
            # 从session_state中删除
            if key in st.session_state:
                try:
                    del st.session_state[key]
                    debug_log(f"状态重置 - 已清理session_state键: {key}", "DEBUG")
                except Exception:
                    pass
            # 从统一状态管理中删除
            set_dfm_state(key, None)

        # 🔥 修复：重新设置初始状态，确保页面返回真正的初始状态
        st.session_state['dfm_training_status'] = '等待开始'
        st.session_state['dfm_training_log'] = []
        st.session_state['dfm_training_progress'] = 0
        st.session_state['dfm_model_results_paths'] = None
        st.session_state['dfm_model_results'] = None

        # 🔥 修复：同时更新统一状态管理器
        set_dfm_state('dfm_training_status', '等待开始')
        set_dfm_state('dfm_training_log', [])
        set_dfm_state('dfm_training_progress', 0)
        set_dfm_state('dfm_model_results_paths', None)
        set_dfm_state('dfm_model_results', None)

        debug_log("状态重置 - 已重置所有训练状态到初始值", "DEBUG")

    except Exception as e:
        debug_log(f"状态重置 - 重置失败: {str(e)}", "ERROR")
        # 如果组件化方法失败，使用备用方法
        keys_to_clear = [
            'dfm_training_status',
            'dfm_model_results_paths',
            'dfm_training_log',
            'dfm_training_error',
            'dfm_training_polling_count',
            'dfm_force_reset_training_state',
            'training_completed_refreshed',
            'training_completion_polling_count'
        ]
        for key in keys_to_clear:
            if hasattr(st, 'session_state') and key in st.session_state:
                try:
                    del st.session_state[key]
                except Exception:
                    pass
            set_dfm_state(key, None)

        # 重新设置初始状态
        set_dfm_state('dfm_training_status', '等待开始')
        set_dfm_state('dfm_training_log', [])






    keys_to_clear = [
        'dfm_training_status',
        'dfm_model_results_paths',
        'dfm_training_log',
        'dfm_training_error',
        'existing_results_checked',
        'training_completed_refreshed',
        'training_completion_polling_count',  # 🔥 新增：轮询计数器状态键
        'dfm_force_reset_training_state',
        'dfm_training_completed_timestamp',
        'dfm_force_ui_refresh',
        'dfm_training_in_progress',
        'dfm_training_completion_confirmed'
    ]

    for key in keys_to_clear:
        set_dfm_state(key, None)

    # 重置为初始状态
    set_dfm_state('dfm_training_status', '等待开始')
    set_dfm_state('dfm_training_log', [])

# --- 备份：旧的训练线程函数（已替换为组件化方法）---
# def _run_training_thread(params, st_instance_ref, log_callback):
#     """Helper function to run the training in a separate thread."""
#     # 此函数已被组件化的TrainingStatusComponent._execute_training_thread替代
#     pass

def get_dfm_refactor():
    """获取DFM重构适配器实例（使用全局单例）"""
    try:
        dfm_refactor = get_global_dfm_refactor()
        if dfm_refactor is None:
            raise RuntimeError("全局DFM重构器不可用")

        return dfm_refactor
    except Exception as e:
        raise RuntimeError(f"DFM重构适配器初始化失败: {e}")


def init_training_event_listener(st_instance):
    """初始化训练事件监听器"""
    try:
        # 检查是否已经初始化过事件监听器（使用统一状态管理）
        if not get_dfm_state('training_event_listener_initialized', False):
            # 🔥 修复：强制使用StateEventSystem，确保与训练组件使用同一个实例
            from dashboard.state_management.events.state_events import get_global_event_system, StateEventSystem
            event_manager = get_global_event_system()

            # 🔥 关键修复：验证获取到的是正确的事件管理器类型
            from dashboard.ui.utils.debug_helpers import debug_log
            if not isinstance(event_manager, StateEventSystem):
                debug_log(f"UI事件监听 - 警告：获取到错误的事件管理器类型: {type(event_manager)}", "WARNING")
                # 强制创建正确的事件系统
                event_manager = StateEventSystem(max_history=2000, enable_async=True)
                debug_log("UI事件监听 - 已创建新的StateEventSystem实例", "DEBUG")

            debug_log(f"UI事件监听 - 事件管理器类型验证通过: {type(event_manager)}", "DEBUG")

            # 🔥 修复：使用统一状态管理的事件监听器
            def on_training_status_changed(event):
                # 🔥 修复：处理多种事件类型
                from dashboard.state_management.events.state_events import EventType
                
                # 处理训练完成事件
                if event.event_type == EventType.TRAINING_COMPLETED:
                    debug_log(f"UI事件监听 - 收到训练完成事件: {event.event_type.value}", "DEBUG")
                    new_status = '训练完成'
                # 处理状态变更事件
                elif event.event_type == EventType.STATE_CHANGED and event.key == 'dfm_training_status':
                    new_status = event.new_value
                    debug_log(f"UI事件监听 - 收到状态变更事件: {new_status}", "DEBUG")
                # 处理训练失败事件
                elif event.event_type == EventType.TRAINING_FAILED:
                    debug_log(f"UI事件监听 - 收到训练失败事件", "DEBUG")
                    new_status = '训练失败'
                else:
                    return  # 忽略其他事件
                
                # 原有的处理逻辑
                if True:  # 保持原有缩进
                    new_status = event.new_value
                    debug_log(f"UI事件监听 - 收到训练状态变更事件: {new_status}", "DEBUG")

                    if new_status == '训练完成':
                        debug_log("UI事件监听 - 训练完成，设置UI更新标志", "DEBUG")
                        # 🔥 修复：使用统一状态管理设置UI刷新标志
                        set_dfm_state('ui_refresh_needed', True)
                        set_dfm_state('training_completion_timestamp', datetime.now().isoformat())
                        debug_log("UI事件监听 - UI刷新标志已设置", "DEBUG")
                        
                        # 🔥 新增：确保结果文件路径也被同步到session_state
                        results = event.metadata.get('results') if event.metadata else None
                        if results:
                            st.session_state['dfm_model_results_paths'] = results
                            debug_log(f"UI事件监听 - 结果文件路径已同步: {len(results)}个文件", "DEBUG")

                        # 🔥 修复：不在事件监听器中调用st.rerun()，而是设置标志位
                        # st.rerun() 只能在主线程中调用，事件监听器可能在后台线程中运行
                        try:
                            # 设置强制刷新标志，让主线程检测并执行刷新
                            st.session_state['force_ui_refresh'] = True
                            st.session_state['training_ui_update_needed'] = True
                            debug_log("UI事件监听 - 已设置UI刷新标志，等待主线程处理", "DEBUG")
                        except Exception as e:
                            debug_log(f"UI事件监听 - 设置UI刷新标志失败: {e}", "WARNING")
                            
                    elif new_status and new_status.startswith('训练失败'):
                        debug_log("UI事件监听 - 训练失败，设置UI更新标志", "DEBUG")
                        # 🔥 修复：使用统一状态管理设置UI刷新标志
                        set_dfm_state('ui_refresh_needed', True)
                        set_dfm_state('training_failure_timestamp', datetime.now().isoformat())
                        debug_log("UI事件监听 - UI刷新标志已设置", "DEBUG")

            # 监听训练状态变更事件
            # 🔥 修复：监听多个事件类型，确保能收到训练完成事件
            event_types_to_listen = {
                EventType.STATE_CHANGED,
                EventType.TRAINING_COMPLETED,
                EventType.TRAINING_FAILED,
                EventType.TRAINING_STARTED
            }
            event_manager.add_listener(on_training_status_changed, event_types_to_listen)
            debug_log(f"UI事件监听 - 已注册监听器，监听事件类型: {[e.value for e in event_types_to_listen]}", "DEBUG")

            set_dfm_state('training_event_listener_initialized', True)
            # 注意：event_manager不能序列化，所以不存储到状态管理器中
            debug_log("UI事件监听 - 训练事件监听器初始化成功", "DEBUG")

    except Exception as e:
        debug_log(f"UI事件监听 - 初始化失败: {e}", "ERROR")


def get_dfm_state(key, default=None):
    """获取DFM状态值（使用统一状态管理）"""
    try:
        # 导入调试工具
        from dashboard.ui.utils.debug_helpers import debug_log

        # 🔥 修复：使用统一状态管理器而不是直接访问session_state
        training_keys = [
            'dfm_training_status',
            'dfm_training_log',
            'dfm_training_progress',
            'dfm_model_results_paths',
            'dfm_training_error',
            'dfm_training_start_time',
            'dfm_training_end_time',
            'training_completed_refreshed',
            'training_completion_polling_count'
        ]

        if key in training_keys:
            # 🔥 修复：确保使用与训练组件完全相同的UnifiedStateManager
            from dashboard.state_management.refactor import get_global_dfm_refactor
            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                # 验证使用的是正确的UnifiedStateManager（仅在调试模式下输出）
                if hasattr(dfm_refactor, 'unified_manager'):
                    debug_log(f"前端状态读取 - UnifiedStateManager类型: {type(dfm_refactor.unified_manager)}", "DEBUG")

                value = dfm_refactor.get_dfm_state('train_model', key, default)

                # 详细的状态读取日志（仅在调试模式下输出）
                debug_log(f"前端状态读取 - 键: {key}, 值: {value}, 类型: {type(value).__name__}", "DEBUG")
                debug_log(f"前端状态读取 - DFM重构器类型: {type(dfm_refactor)}", "DEBUG")

                return value
            else:
                debug_log(f"警告 - DFM重构器不可用，键: {key}", "WARNING")
                return default

        # 数据相关的键从data_prep命名空间获取
        dfm_refactor = get_dfm_refactor()
        if dfm_refactor:
            data_keys = [
                'dfm_prepared_data_df',
                'dfm_transform_log_obj',
                'dfm_industry_map_obj',
                'dfm_removed_vars_log_obj',
                'dfm_var_type_map_obj',
                'dfm_param_data_start_date',
                'dfm_param_data_end_date'
            ]

            if key in data_keys:
                return dfm_refactor.get_dfm_state('data_prep', key, default)

            # 其他键从train_model命名空间获取
            return dfm_refactor.get_dfm_state('train_model', key, default)
        else:
            return default
    except Exception as e:
        from dashboard.ui.utils.debug_helpers import debug_log
        debug_log(f"状态读取 - 异常 - 键: {key}, 错误: {str(e)}", "ERROR")
        return default


def set_dfm_state(key, value):
    """设置DFM状态值（修复缓存不一致问题）"""
    try:
        # 🔥 关键修复：训练相关状态直接写入session_state，确保一致性
        training_keys = [
            'dfm_training_status',
            'dfm_training_log',
            'dfm_training_progress',
            'dfm_model_results_paths',
            'dfm_training_error',
            'dfm_training_start_time',
            'dfm_training_end_time',
            'training_completed_refreshed',
            'training_completion_polling_count'
        ]

        if key in training_keys:
            # 🔥 修复：使用统一状态管理器而不是直接写入session_state
            from dashboard.state_management.refactor import get_global_dfm_refactor
            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                success = dfm_refactor.set_dfm_state('train_model', key, value)

                # 🔥 优化：只在调试模式下打印详细日志
                from dashboard.ui.utils.debug_helpers import debug_log
                debug_mode = dfm_refactor.get_dfm_state('train_model', 'dfm_debug_mode', False)
                if debug_mode:
                    debug_log(f"统一状态设置 - 键: {key}, 值类型: {type(value)}, 成功: {success}", "DEBUG")

                # 同时尝试组件化方法，确保双重写入
                try:
                    training_component = get_training_status_component()
                    training_component._set_state(key, value)
                    if debug_mode:
                        debug_log(f"组件状态设置 - 键: {key}, 值类型: {type(value)}, 成功: True", "DEBUG")
                except Exception as e:
                    if debug_mode:
                        debug_log(f"组件状态设置失败 - 键: {key}, 错误: {str(e)}", "ERROR")

                return success
            else:
                debug_log(f"警告 - DFM统一状态管理器不可用，无法设置键: {key}", "WARNING")
                return False

        # 其他状态使用原有方法
        from dashboard.ui.utils.debug_helpers import debug_log
        dfm_refactor = get_dfm_refactor()
        if dfm_refactor:
            success = dfm_refactor.set_dfm_state('train_model', key, value)
            debug_log(f"状态设置 - 键: {key}, 值类型: {type(value)}, 成功: {success}", "DEBUG")
            return success
        else:
            debug_log(f"状态设置 - 失败 - DFM重构器不可用, 键: {key}", "WARNING")
            return False
    except Exception as e:
        debug_log(f"状态设置 - 异常 - 键: {key}, 错误: {str(e)}", "ERROR")
        import traceback
        debug_log(f"状态设置 - 异常堆栈: {traceback.format_exc()}", "ERROR")
        return False


# --- 辅助函数 ---
def convert_to_datetime(date_input):
    """将日期输入转换为datetime对象"""
    if date_input is None:
        return None

    if isinstance(date_input, datetime):
        return date_input
    elif isinstance(date_input, date):
        return datetime.combine(date_input, time.min)
    elif isinstance(date_input, str):
        try:
            return datetime.strptime(date_input, '%Y-%m-%d')
        except ValueError:
            try:
                return datetime.strptime(date_input, '%Y/%m/%d')
            except ValueError:
                return None
    else:
        return None


def process_training_events(st_instance) -> bool:
    """
    处理训练事件并更新UI状态

    Args:
        st_instance: Streamlit实例

    Returns:
        bool: 是否需要重新渲染UI
    """
    try:
        event_manager = get_event_manager()

        # 检查是否有待处理事件
        if not event_manager.has_pending_events():
            return False

        # 获取所有待处理事件
        pending_events = event_manager.get_pending_events()
        if not pending_events:
            return False

        # 创建事件处理器
        event_handler = TrainingEventHandler(
            state_setter_func=set_dfm_state,
            state_getter_func=get_dfm_state
        )

        # 处理事件
        result = event_handler.process_events(pending_events)

        logger.info(f"Processed {result['processed_count']} events, "
                   f"UI update required: {result['ui_update_required']}")

        # 如果有错误，记录但不中断
        if result['errors']:
            for error in result['errors']:
                logger.error(f"Event processing error: {error}")

        return result['ui_update_required']

    except Exception as e:
        logger.error(f"Error in process_training_events: {e}")
        return False


def render_dfm_train_model_tab(st_instance):
    # 🔥 修复：在函数开头声明全局变量

    # 🔥 新增：自动刷新机制 - 在训练期间定期刷新页面
    auto_refresh_enabled = st.session_state.get('auto_refresh_enabled', False)
    training_start_time = st.session_state.get('training_start_time', 0)
    current_time = time.time()
    last_auto_refresh = st.session_state.get('last_auto_refresh', 0)

    if auto_refresh_enabled:
        # 检查训练是否已完成
        current_training_status = get_dfm_state('dfm_training_status', '等待开始')

        if current_training_status == '训练完成':
            # 训练完成，停止自动刷新
            st.session_state['auto_refresh_enabled'] = False
            print("🔥 [自动刷新] 训练完成，停止自动刷新")
        elif current_training_status == '正在训练...' and current_time - training_start_time < 300:  # 5分钟超时
            # 训练中，每2秒刷新一次（不使用sleep，而是检查时间间隔）
            if current_time - last_auto_refresh >= 2:
                st.session_state['last_auto_refresh'] = current_time
                print("🔥 [自动刷新] 训练中，执行定期刷新")
                st_instance.rerun()
                return
        elif current_time - training_start_time >= 300:
            # 超时，停止自动刷新
            st.session_state['auto_refresh_enabled'] = False
            print("🔥 [自动刷新] 训练超时，停止自动刷新")

    # 🔥 新增：页面开始时立即检查是否有待处理的UI更新
    training_ui_update_needed = st.session_state.get('training_ui_update_needed', False)
    force_ui_refresh = st.session_state.get('force_ui_refresh', False)
    training_completion_trigger = st.session_state.get('training_completion_trigger', 0)
    last_processed_trigger = st.session_state.get('last_processed_trigger', 0)

    # 🔥 新增：基于时间戳的训练完成检测
    training_just_completed = training_completion_trigger > last_processed_trigger

    if training_ui_update_needed or force_ui_refresh or training_just_completed:
        print(f"🔥 [页面开始] 检测到待处理的UI更新，立即处理 - UI标志: {training_ui_update_needed}, 强制刷新: {force_ui_refresh}, 训练完成: {training_just_completed}")
        # 清除标志
        st.session_state['training_ui_update_needed'] = False
        st.session_state['force_ui_refresh'] = False
        if training_just_completed:
            st.session_state['last_processed_trigger'] = training_completion_trigger
            # 停止自动刷新
            st.session_state['auto_refresh_enabled'] = False
        # 立即刷新
        st_instance.rerun()
        return

    # 🔥 事件驱动：初始化训练事件监听器
    init_training_event_listener(st_instance)

    # 🔥 新增：事件驱动的状态更新
    ui_needs_update = process_training_events(st_instance)
    if ui_needs_update:
        logger.info("Events processed, UI will be updated")

    # 确保datetime在函数开头就可用
    from datetime import datetime
    import time

    # 🔥 事件驱动：移除旧的轮询回调函数，现在使用事件系统

    # 🔥 新增：页面加载时清理超时的下载状态
    cleanup_expired_downloads()

    # --- 🔥 修复：智能的错误显示逻辑 ---
    if _TRAIN_UI_IMPORT_ERROR_MESSAGE:
        if "train_and_save_dfm_results" in _TRAIN_UI_IMPORT_ERROR_MESSAGE:
            st_instance.error(f"关键模块导入错误，模型训练功能不可用:\n{_TRAIN_UI_IMPORT_ERROR_MESSAGE}")
            return  # 如果训练函数不可用，直接返回
        else:
            # 如果只是数据准备模块的导入问题，显示警告但继续
            st_instance.warning("⚠️ 数据准备模块导入警告，但映射数据传递已修复，功能应该正常")
    else:
        # 如果没有错误消息，显示成功信息
        st_instance.success("✅ 所有必需模块已成功加载，模型训练功能可用")

    # --- 初始化DFM模块状态变量（兼容新旧状态管理） ---
    # 🔥 修复：检查是否需要重置训练状态（解决文件下载按钮提前出现的问题）
    current_training_status = get_dfm_state('dfm_training_status')
    current_model_results = get_dfm_state('dfm_model_results_paths')

    # 如果页面刚加载且存在之前的训练完成状态，询问用户是否要重置
    page_just_loaded = get_dfm_state('dfm_page_initialized') is None

    if page_just_loaded:
        set_dfm_state('dfm_page_initialized', True)

        # 如果存在之前的训练结果，显示提示并自动重置（避免混淆）
        if current_training_status == '训练完成' and current_model_results:
            st_instance.info("🔄 检测到之前的训练结果，已自动重置训练状态以开始新的训练")
            _reset_training_state()
            current_training_status = '等待开始'

    # 使用状态管理器初始化DFM状态
    if current_training_status is None:
        set_dfm_state('dfm_training_status', "等待开始")
    if get_dfm_state('dfm_model_results') is None:
        set_dfm_state('dfm_model_results', None)
    if get_dfm_state('dfm_training_log') is None:
        set_dfm_state('dfm_training_log', [])
    if get_dfm_state('dfm_model_results_paths') is None:
        set_dfm_state('dfm_model_results_paths', None)



    # --- 状态重置检查 ---
    # 状态重置现在仅通过用户点击按钮触发

    # === 自动检测功能已禁用 - 用户不希望自动恢复训练状态 ===

    # 移除已有训练结果检测功能，不再检查dym_estimate目录
    def _detect_existing_results():
        """不再检测已存在的训练结果文件，所有结果通过UI下载获得"""
        return None

    # 检测已有结果并更新状态（兼容新旧状态管理）
    if (get_dfm_state('dfm_training_status') == '等待开始' and
        get_dfm_state('existing_results_checked') is None):

        set_dfm_state('existing_results_checked', True)
        existing_results = _detect_existing_results()

        if existing_results:
            # 更新全局状态和状态管理器
            set_dfm_state('dfm_training_status', '训练完成')
            set_dfm_state('dfm_model_results_paths', existing_results)
            set_dfm_state('dfm_training_log', ['[自动检测] 发现已有训练结果，已自动加载'])

            # 刷新UI显示
            st_instance.rerun()





    # 🔥 移除临时测试代码 - 现在使用真实的训练状态

    # 🔥 修复：完全使用统一状态管理，移除session_state依赖
    training_status = get_dfm_state('dfm_training_status', '等待开始')

    # 🔥 新增：检查训练结果是否存在但状态未更新的情况
    training_results = get_dfm_state('dfm_model_results_paths')
    if training_results and training_status != '训练完成':
        print(f"🔥 [状态修复] 检测到训练结果存在但状态未更新: {training_status} -> 训练完成")
        set_dfm_state('dfm_training_status', '训练完成')
        training_status = '训练完成'

    # 🔥 关键修复：检查线程安全的UI刷新标志
    ui_refresh_needed = get_dfm_state('ui_refresh_needed', False)
    training_completion_timestamp = get_dfm_state('training_completion_timestamp')

    # 检查是否有新的训练完成（基于时间戳）
    last_processed_timestamp = get_dfm_state('last_processed_completion_timestamp')
    training_just_completed = (training_completion_timestamp and
                              training_completion_timestamp != last_processed_timestamp)

    # 合并刷新标志
    force_ui_refresh = ui_refresh_needed or training_just_completed

    # 🔥 新增：强制检查训练完成状态（仅在调试模式下输出）
    from dashboard.ui.utils.debug_helpers import debug_log
    debug_log(f"UI状态检查 - 当前训练状态: {training_status}", "DEBUG")
    debug_log(f"UI状态检查 - UI刷新需要标志: {ui_refresh_needed}", "DEBUG")
    debug_log(f"UI状态检查 - 强制刷新标志: {force_ui_refresh}", "DEBUG")
    debug_log(f"UI状态检查 - 训练刚完成标志: {training_just_completed}", "DEBUG")

    # 🔥 新增：检查事件监听器设置的UI更新标志
    training_ui_update_needed = st.session_state.get('training_ui_update_needed', False)

    # 🔥 新增：处理强制UI刷新（完全使用统一状态管理）
    if force_ui_refresh or training_just_completed or training_ui_update_needed:
        print("🔥 [UI更新] 检测到强制刷新标志，清除标志并刷新UI")

        # 清除刷新标志（使用统一状态管理）
        set_dfm_state('ui_refresh_needed', False)
        if training_completion_timestamp:
            set_dfm_state('last_processed_completion_timestamp', training_completion_timestamp)

        if training_status == '训练完成':
            set_dfm_state('training_completed_refreshed', True)

        # 🔥 新增：清除事件监听器设置的标志
        if training_ui_update_needed:
            st.session_state['training_ui_update_needed'] = False
            print("🔥 [UI更新] 清除事件监听器UI更新标志")

        st_instance.rerun()

    # 🔥 新增：训练中时的定期状态检查和自动刷新机制
    elif training_status == '正在训练...':
        # 🔥 新增：使用 st.empty() 容器实现自动刷新
        if 'training_status_container' not in st.session_state:
            st.session_state['training_status_container'] = st_instance.empty()

        # 检查是否需要定期刷新以显示训练进度
        import time
        current_time = time.time()
        last_refresh_time = get_dfm_state('last_training_refresh_time', 0)

        # 每2秒检查一次训练状态（进一步缩短间隔）
        if current_time - last_refresh_time > 2:
            set_dfm_state('last_training_refresh_time', current_time)

            # 🔥 新增：在定期检查中也检测训练完成标志
            training_completion_trigger = st.session_state.get('training_completion_trigger', 0)
            last_processed_trigger = st.session_state.get('last_processed_trigger', 0)

            if training_completion_trigger > last_processed_trigger:
                print("🔥 [定期检查] 检测到训练完成，立即刷新UI")
                st.session_state['last_processed_trigger'] = training_completion_trigger
                # 清除训练状态容器
                if 'training_status_container' in st.session_state:
                    del st.session_state['training_status_container']
                st_instance.rerun()
            else:
                print("🔥 [定期检查] 训练中，刷新UI以显示最新状态")
                # 🔥 新增：使用 st.rerun() 强制刷新整个页面
                st_instance.rerun()

    # 🔥 修复：备用检查机制，确保训练完成状态能被检测到
    elif training_status == '训练完成':
        training_completed_refreshed = get_dfm_state('training_completed_refreshed')
        print(f"🔥 [备用检查] 训练完成状态检查 - 已刷新标志: {training_completed_refreshed}")

        if training_completed_refreshed is None:
            print("🔥 [备用检查] 检测到训练完成但未刷新，执行备用刷新")
            set_dfm_state('training_completed_refreshed', True)

            # 🔥 关键修复：清除Streamlit缓存，确保状态一致性
            try:
                st_instance.cache_resource.clear()
                print("🔥 [缓存清除] Streamlit资源缓存已清除")
            except Exception as e:
                print(f"🔥 [缓存清除] 清除Streamlit缓存失败: {e}")

            debug_training_state("备用检查触发UI刷新", show_in_ui=True)
            st_instance.rerun()
        else:
            print("🔥 [备用检查] 训练完成状态已处理，继续显示结果")

    # 🔥 修复：增强备用检查机制，确保训练完成状态能被检测到
    elif training_status == '训练完成':
        training_completed_refreshed = get_dfm_state('training_completed_refreshed')
        print(f"🔥 [备用检查] 训练完成状态检查 - 已刷新标志: {training_completed_refreshed}")

        if training_completed_refreshed is None:
            print("🔥 [备用检查] 检测到训练完成但未刷新，执行备用刷新")
            set_dfm_state('training_completed_refreshed', True)
            debug_training_state("备用检查触发UI刷新", show_in_ui=True)
            st_instance.rerun()
        else:
            print("🔥 [备用检查] 训练完成状态已处理，继续显示结果")

    # else:
    #     pass  # 跳过页面刷新

    # --- 数据加载与准备 ---
    input_df = get_dfm_state('dfm_prepared_data_df')

    available_target_vars = []
    if input_df is not None:
        # 从已加载数据中获取可选的目标变量
        available_target_vars = [col for col in input_df.columns if 'date' not in col.lower() and 'time' not in col.lower() and col not in getattr(config, 'EXCLUDE_COLS_FROM_TARGET', [])]

        # 🔥 重要修复：确保默认目标变量始终包含在选项中
        default_target = None
        if hasattr(config, 'TARGET_VARIABLE'):
            default_target = config.TARGET_VARIABLE
        else:
            default_target = '规模以上工业增加值:当月同比'  # 硬编码的默认值

        # 如果默认目标变量在数据中存在但不在过滤列表中，则添加它
        if default_target and default_target in input_df.columns and default_target not in available_target_vars:
            available_target_vars.insert(0, default_target)  # 插入到开头作为首选


        # 如果过滤后的列表为空，但默认目标变量存在，则使用它
        if not available_target_vars and default_target and default_target in input_df.columns:
            available_target_vars = [default_target]


        if not available_target_vars:
            st_instance.warning("预处理数据中未找到合适的目标变量候选。")
            # 即使没找到，也提供一个默认选项避免selectbox为空
            if default_target:
                available_target_vars = [default_target]
                st_instance.info(f"使用默认目标变量: {default_target}")


    else:

        st_instance.warning("数据尚未准备，请先在\"数据准备\"选项卡中处理数据。变量选择功能将受限。")

        # 🔥 修复：即使没有数据，也提供默认目标变量选项以避免selectbox为空
        default_target = None
        if hasattr(config, 'TARGET_VARIABLE'):
            default_target = config.TARGET_VARIABLE
        else:
            default_target = '规模以上工业增加值:当月同比'  # 硬编码的默认值

        if default_target:
            available_target_vars = [default_target]
            st_instance.info(f"使用默认目标变量: {default_target}")


    # 🔥 优化：加载行业与指标映射 - 优先使用已准备好的映射数据
    # 1. 首先尝试从统一状态管理器中获取已经准备好的映射数据
    available_data_columns = list(input_df.columns) if input_df is not None else None



    # 优先使用统一状态管理器中的映射数据
    map_data = load_mappings_from_unified_state(available_data_columns)

    if map_data and all(x is not None for x in map_data):
        unique_industries, var_to_indicators_map_by_industry, _ = map_data

        st_instance.success(f"✅ 已加载映射数据：{len(unique_industries)} 个行业，{sum(len(v) for v in var_to_indicators_map_by_industry.values())} 个指标")
    else:
        # 🚫 不再回退到Excel文件，使用空映射继续（数据准备模块应该正确保存映射）

        st_instance.warning("⚠️ 未找到映射数据，请确保已在'数据准备'模块正确处理数据")
        unique_industries = []
        var_to_indicators_map_by_industry = {}

    # 主布局：现在是上下结构，不再使用列
    # REMOVED: var_selection_col, param_col = st_instance.columns([1, 1.5])

    # --- 变量选择部分 (之前在 var_selection_col) ---

    # 1. 选择目标变量（兼容新旧状态管理）
    # 🔥 修复：确保目标变量状态始终有效
    if available_target_vars:
        # 初始化目标变量状态
        if get_dfm_state('dfm_target_variable') is None:
            set_dfm_state('dfm_target_variable', available_target_vars[0])

        current_target_var = get_dfm_state('dfm_target_variable')

        # 确保当前目标变量在可选列表中
        if current_target_var not in available_target_vars:
            current_target_var = available_target_vars[0]
            set_dfm_state('dfm_target_variable', current_target_var)

        selected_target_var = st_instance.selectbox(
            "**选择目标变量**",
            options=available_target_vars,
            index=available_target_vars.index(current_target_var),
            key="ss_dfm_target_variable",
            help="选择您希望模型预测的目标序列。"
        )
        set_dfm_state('dfm_target_variable', selected_target_var)
    else:
        # 🔥 紧急情况：如果仍然没有可选变量，显示错误并设置为None
        st_instance.error("❌ 无法找到任何可用的目标变量")
        set_dfm_state('dfm_target_variable', None)


    # 2. 选择行业变量 (复选框形式，默认全选)（兼容新旧状态管理）
    st_instance.markdown("**选择行业**")

    # 添加操作说明
    with st_instance.expander("💡 操作说明", expanded=False):
        st_instance.markdown("""
        **行业选择操作指南：**

        1. **直接点击复选框**：点击任意行业复选框来选择/取消选择该行业
        2. **批量操作按钮**：
           - 🔄 **重置**：清除所有选择状态，恢复为默认的全选状态
           - **取消全行业**：取消选择所有行业
           - **选择全行业**：选择所有行业

        **选择PMI的步骤：**
        1. 点击"🔄 重置"按钮确保处于默认状态
        2. 点击"取消全行业"按钮清除所有选择
        3. 单独点击"PMI"复选框进行选择
        4. 确认下方显示"已选择 1 个行业"

        **注意**：选择行业后，下方会自动显示对应的预测指标供进一步选择。
        """)

    st_instance.markdown("---")

    # 🔥 修复：检查复选框状态是否需要初始化
    current_checkbox_states = get_dfm_state('dfm_industry_checkbox_states', None)

    # 如果状态为None、空字典，或者行业列表发生变化，则重新初始化
    needs_initialization = (
        current_checkbox_states is None or
        not current_checkbox_states or  # 空字典
        (unique_industries and set(current_checkbox_states.keys()) != set(unique_industries))  # 行业列表变化
    )

    if needs_initialization and unique_industries:
        initial_states = {industry: True for industry in unique_industries}
        set_dfm_state('dfm_industry_checkbox_states', initial_states)
    elif not unique_industries:
        set_dfm_state('dfm_industry_checkbox_states', {})

    # 为了避免在没有行业时出错，检查 unique_industries
    if not unique_industries:
        st_instance.info("没有可用的行业数据。")
    else:
        # 创建列以更好地布局复选框
        if CONFIG_AVAILABLE:
            num_cols_industry = UIDefaults.NUM_COLS_INDUSTRY
        else:
            num_cols_industry = 3

        industry_cols = st_instance.columns(num_cols_industry)
        col_idx = 0
        current_checkbox_states = get_dfm_state('dfm_industry_checkbox_states', {})

        # 创建复选框并收集状态（纯统一状态管理器实现）
        for industry_name in unique_industries:
            with industry_cols[col_idx % num_cols_industry]:
                # 获取当前状态：从统一状态管理器获取，默认为True
                current_value = current_checkbox_states.get(industry_name, True)

                # 不使用key参数，避免session_state冲突
                new_state = st_instance.checkbox(
                    industry_name,
                    value=current_value
                )
                current_checkbox_states[industry_name] = new_state
            col_idx += 1

        # 更新状态管理器
        set_dfm_state('dfm_industry_checkbox_states', current_checkbox_states)

        # 使用按钮控制行业选择，通过统一状态管理器同步
        col_deselect, col_select, col_reset = st_instance.columns(3)
        with col_deselect:
            if st_instance.button("取消全行业",
                                key='btn_dfm_deselect_all_industries',
                                help="点击取消所有已选中的行业",
                                use_container_width=True):
                # 只更新统一状态管理器中的状态
                set_dfm_state('dfm_industry_checkbox_states', {industry: False for industry in unique_industries})
                # 强制刷新页面以更新UI
                st_instance.rerun()

        with col_select:
            if st_instance.button("选择全行业",
                                key='btn_dfm_select_all_industries',
                                help="点击选择所有行业",
                                use_container_width=True):
                # 只更新统一状态管理器中的状态
                set_dfm_state('dfm_industry_checkbox_states', {industry: True for industry in unique_industries})
                # 强制刷新页面以更新UI
                st_instance.rerun()

        with col_reset:
            if st_instance.button("🔄 重置",
                                key='btn_dfm_reset_industries',
                                help="重置为默认状态（全选）",
                                use_container_width=True):
                # 直接设置为全选状态，而不是清空
                reset_states = {industry: True for industry in unique_industries}
                set_dfm_state('dfm_industry_checkbox_states', reset_states)
                # 强制刷新页面以更新UI
                st_instance.rerun()

        # 显示当前选择状态
        selected_count = sum(1 for checked in current_checkbox_states.values() if checked)
        st_instance.info(f"📊 当前状态：已选择 {selected_count} 个行业（共 {len(unique_industries)} 个可选）")

    # 更新当前选中的行业列表（兼容新旧状态管理）
    current_checkbox_states = get_dfm_state('dfm_industry_checkbox_states', {})

    # 如果复选框状态为空但有行业数据，使用默认全选状态
    if not current_checkbox_states and unique_industries:
        current_checkbox_states = {industry: True for industry in unique_industries}
        set_dfm_state('dfm_industry_checkbox_states', current_checkbox_states)

    selected_industries = [
        industry for industry, checked in current_checkbox_states.items() if checked
    ]

    set_dfm_state('dfm_selected_industries', selected_industries)

    # 3. 根据选定行业选择预测指标 (每个行业一个多选下拉菜单，默认全选)
    st_instance.markdown("**选择预测指标**")
    # 初始化指标选择状态
    if get_dfm_state('dfm_selected_indicators_per_industry', None) is None:
        set_dfm_state('dfm_selected_indicators_per_industry', {})

    final_selected_indicators_flat = []
    current_selected_industries = get_dfm_state('dfm_selected_industries', [])

    if not current_selected_industries:
        st_instance.info("请先在上方选择至少一个行业。")
    else:
        # 🔥 优化：批量处理状态更新，减少重复调用
        current_selection = get_dfm_state('dfm_selected_indicators_per_industry', {})

        for industry_name in current_selected_industries:
            st_instance.markdown(f"**行业: {industry_name}**")
            indicators_for_this_industry = var_to_indicators_map_by_industry.get(industry_name, [])

            if not indicators_for_this_industry:
                st_instance.text(f"  (该行业无可用指标)")
                current_selection[industry_name] = []
                continue

            # 默认选中该行业下的所有指标
            default_selection_for_industry = current_selection.get(
                industry_name,
                indicators_for_this_industry # 默认全选
            )
            # 确保默认值是实际可选列表的子集
            valid_default = [item for item in default_selection_for_industry if item in indicators_for_this_industry]
            if not valid_default and indicators_for_this_industry: # 如果之前存的默认值无效了，且当前有可选指标，则全选
                valid_default = indicators_for_this_industry

            # 移除回调函数，改为直接逻辑处理

            # 取消全选复选框（不使用session_state）
            deselect_all_checked = st_instance.checkbox(
                f"取消全选 {industry_name} 指标",
                help=f"勾选此框将取消所有已为 '{industry_name}' 选中的指标。"
            )

            # 如果取消全选被勾选，清空该行业的选择
            if deselect_all_checked:
                valid_default = []

            selected_in_widget = st_instance.multiselect(
                f"为 '{industry_name}' 选择指标",
                options=indicators_for_this_industry,
                default=valid_default,
                help=f"从 {industry_name} 行业中选择预测指标。"
            )

            # 🔥 优化：直接更新当前选择字典，避免重复读取状态
            current_selection[industry_name] = selected_in_widget
            final_selected_indicators_flat.extend(selected_in_widget)

        # 🔥 优化：清理不再被选中的行业条目
        industries_to_remove_from_state = [
            ind for ind in current_selection
            if ind not in current_selected_industries
        ]
        for ind_to_remove in industries_to_remove_from_state:
            del current_selection[ind_to_remove]

        # 🔥 优化：一次性更新状态，而不是在循环中多次更新
        set_dfm_state('dfm_selected_indicators_per_industry', current_selection)

    # 更新最终的扁平化预测指标列表 (去重)
    final_indicators = sorted(list(set(final_selected_indicators_flat)))
    set_dfm_state('dfm_selected_indicators', final_indicators)

    # 变量选择完成

    # 显示汇总信息 (可选)
    st_instance.markdown("--- ")
    current_target_var = get_dfm_state('dfm_target_variable', None)
    current_selected_indicators = get_dfm_state('dfm_selected_indicators', [])
    st_instance.text(f" - 目标变量: {current_target_var if current_target_var else '未选择'}")
    st_instance.text(f" - 选定行业数: {len(current_selected_industries)}")
    st_instance.text(f" - 选定预测指标总数: {len(current_selected_indicators)}")

    # with st_instance.expander("查看已选指标列表"):
    #     st_instance.json(session_state.dfm_selected_indicators if session_state.dfm_selected_indicators else [])

    # --- 模型参数配置部分 (之前在 param_col) ---
    st_instance.markdown("--- ") # 分隔线，将变量选择与参数配置分开
    st_instance.subheader("模型参数")

    # 创建三列布局
    col1_time, col2_factor_core, col3_factor_specific = st_instance.columns(3)

    # --- 第一列: 时间窗口设置 ---
    with col1_time:


        # 计算基于数据的智能默认值
        def get_data_based_date_defaults():
            """基于实际数据计算日期默认值，优先使用数据准备页面设置的日期边界"""
            from datetime import datetime, timedelta
            today = datetime.now().date()

            # 🔥 新增：优先使用数据准备页面设置的日期边界
            data_prep_start = get_dfm_state('dfm_param_data_start_date')
            data_prep_end = get_dfm_state('dfm_param_data_end_date')

            # 🔥 修复：验证期应该是历史期间，不应该包含未来日期
            static_defaults = {
                'training_start': data_prep_start if data_prep_start else datetime(today.year - 5, 1, 1).date(),
                'validation_start': datetime(2024, 7, 1).date(),  # 2024年7月1日
                'validation_end': datetime(2024, 12, 31).date()  # 🔥 修复：验证期结束于2024年12月31日
            }

            try:
                # 🔥 修复：使用多键名获取数据
                data_df = None
                for key in ['dfm_prepared_data_df', 'data_prep.dfm_prepared_data_df', 'dfm.data_prep.dfm_prepared_data_df']:
                    df_value = get_dfm_state(key)
                    if df_value is not None:
                        data_df = df_value
                        break
                if data_df is not None and isinstance(data_df.index, pd.DatetimeIndex) and len(data_df.index) > 0:
                    # 从数据获取第一期和最后一期
                    data_first_date = data_df.index.min().date()  # 第一期数据
                    data_last_date = data_df.index.max().date()   # 最后一期数据

                    # 重要：确保数据的最后日期不是未来日期
                    if data_last_date > today:
                        print(f"⚠️ 警告: 数据包含未来日期 {data_last_date}，将使用今天作为最后日期")
                        data_last_date = today

                    # 🔥 修复：训练开始日期优先使用数据准备页面设置的边界
                    if data_prep_start:
                        training_start_date = data_prep_start
                    elif data_first_date:
                        # 确保不早于合理的历史范围（2020年）
                        reasonable_start = datetime(2020, 1, 1).date()
                        training_start_date = max(data_first_date, reasonable_start)
                        print(f"⚠️ [日期回退] 数据准备页面未设置，使用数据文件日期（限制在2020年后）: {training_start_date}")
                        print(f"   原始数据开始日期: {data_first_date}")
                    else:
                        training_start_date = datetime(2020, 1, 1).date()
                        print(f"⚠️ [日期默认] 使用硬编码默认值: {training_start_date}")

                    # 计算验证期开始日期：使用数据时间范围的80%作为训练期
                    if data_prep_start and data_prep_end:
                        # 如果数据准备页面设置了边界，基于边界计算
                        total_days = (data_prep_end - data_prep_start).days
                        training_days = int(total_days * 0.8)
                        validation_start_date = data_prep_start + timedelta(days=training_days)
                    else:
                        # 否则基于实际数据计算
                        total_days = (data_last_date - data_first_date).days
                        training_days = int(total_days * 0.8)
                        validation_start_date = data_first_date + timedelta(days=training_days)

                    # 确保验证期开始日期不是未来日期
                    if validation_start_date > today:
                        validation_start_date = today - timedelta(days=30)  # 1个月前

                    # 🔥 修复：验证期结束日期必须是历史期间，不能包含未来
                    # 验证期用于测试模型性能，必须使用历史数据
                    validation_end_date = datetime(2024, 12, 31).date()  # 🔥 强制使用2024年底作为验证期结束

                    # 验证日期逻辑的合理性
                    if validation_start_date >= validation_end_date:
                        # 如果验证期开始晚于或等于结束，重新计算
                        # 🔥 修复：验证期结束日期必须是历史期间
                        validation_end_date = datetime(2024, 12, 31).date()  # 🔥 强制使用2024年底
                        validation_start_date = validation_end_date - timedelta(days=90)  # 验证期3个月

                    return {
                        'training_start': training_start_date,       # 🔥 训练开始日：优先使用数据准备页面设置
                        'validation_start': validation_start_date,   # 验证开始日：计算得出
                        'validation_end': validation_end_date        # 🔥 验证结束日：优先使用数据准备页面设置
                    }
                else:
                    return static_defaults
            except Exception as e:
                print(f"⚠️ 计算数据默认日期失败: {e}，使用静态默认值")
                return static_defaults

        # 获取智能默认值
        date_defaults = get_data_based_date_defaults()

        # 🔥 修复：使用统一状态管理器检查是否有数据并获取数据
        has_data = False
        data_df = None
        for key in ['dfm_prepared_data_df', 'data_prep.dfm_prepared_data_df', 'dfm.data_prep.dfm_prepared_data_df']:
            df_value = get_dfm_state(key)
            if df_value is not None:
                has_data = True
                data_df = df_value
                break
        if has_data:
            if isinstance(data_df.index, pd.DatetimeIndex) and len(data_df.index) > 0:
                # 计算数据的实际日期范围用于比较
                actual_data_start = data_df.index.min().date()
                actual_data_end = data_df.index.max().date()

                # 强制更新统一状态管理器中的日期默认值（检查是否为静态默认值或与数据不匹配）
                current_training_start = get_dfm_state('dfm_training_start_date')
                if (current_training_start == datetime(2010, 1, 1).date() or
                    current_training_start is None or
                    current_training_start != actual_data_start):
                    set_dfm_state('dfm_training_start_date', date_defaults['training_start'])

                current_validation_start = get_dfm_state('dfm_validation_start_date')
                if (current_validation_start == datetime(2020, 12, 31).date() or
                    current_validation_start is None):
                    set_dfm_state('dfm_validation_start_date', date_defaults['validation_start'])

                current_validation_end = get_dfm_state('dfm_validation_end_date')
                if (current_validation_end == datetime(2022, 12, 31).date() or
                    current_validation_end is None):
                    # 🔥 修复：移除与actual_data_end的比较，避免强制使用未来日期
                    set_dfm_state('dfm_validation_end_date', date_defaults['validation_end'])

                # 简化数据范围信息
                data_start = data_df.index.min().strftime('%Y-%m-%d')
                data_end = data_df.index.max().strftime('%Y-%m-%d')
                data_count = len(data_df.index)
                # 🔥 删除蓝色提示信息（用户要求移除）
                # st_instance.info(f"📊 数据: {data_start} 至 {data_end} ({data_count}点)")




        # 🔥 新增：日期参数一致性验证

        # 执行日期一致性验证
        validate_date_consistency()

        # 1. 训练期开始日期
        training_start_value = st_instance.date_input(
            "训练期开始日期 (Training Start Date)",
            value=get_dfm_state('dfm_training_start_date', date_defaults['training_start']),
            key='dfm_training_start_date_input',
            help="选择模型训练数据的起始日期。默认为数据的第一期。"
        )
        set_dfm_state('dfm_training_start_date', training_start_value)

        # 2. 验证期开始日期
        validation_start_value = st_instance.date_input(
            "验证期开始日期 (Validation Start Date)",
            value=get_dfm_state('dfm_validation_start_date', date_defaults['validation_start']),
            key='dfm_validation_start_date_input',
            help="选择验证期开始日期。默认为最后一期数据前3个月。"
        )
        set_dfm_state('dfm_validation_start_date', validation_start_value)

        # 3. 验证期结束日期
        validation_end_value = st_instance.date_input(
            "验证期结束日期 (Validation End Date)",
            value=get_dfm_state('dfm_validation_end_date', date_defaults['validation_end']),
            key='dfm_validation_end_date_input',
            help="选择验证期结束日期。默认为数据的最后一期。"
        )
        set_dfm_state('dfm_validation_end_date', validation_end_value)

    # --- 第二列: 变量选择参数 ---
    with col2_factor_core:


        # 🔥 新增：变量选择方法
        if CONFIG_AVAILABLE:
            variable_selection_options = UIDefaults.VARIABLE_SELECTION_OPTIONS
            default_var_method = TrainDefaults.VARIABLE_SELECTION_METHOD
        else:
            variable_selection_options = {
                'none': "无筛选 (使用全部已选变量)",
                'global_backward': "全局后向剔除 (在已选变量中筛选)"
            }
            default_var_method = 'none'  # 🔥 紧急修复：强制默认为none

        # 获取当前变量选择方法
        current_var_method = get_dfm_state('dfm_variable_selection_method', default_var_method)

        var_method_value = st_instance.selectbox(
            "变量选择方法",
            options=list(variable_selection_options.keys()),
            format_func=lambda x: variable_selection_options[x],
            index=list(variable_selection_options.keys()).index(current_var_method),
            key='dfm_variable_selection_method_input',
            help=(
                "选择在已选变量基础上的筛选方法：\n"
                "- 无筛选: 直接使用所有已选择的变量\n"
                "- 全局后向剔除: 从已选变量开始，逐个剔除不重要的变量"
            )
        )
        set_dfm_state('dfm_variable_selection_method', var_method_value)

        # 🔥 修复：根据选择的方法确定是否启用变量选择
        enable_var_selection = (var_method_value != 'none')
        set_dfm_state('dfm_enable_variable_selection', enable_var_selection)

        # 🔥 修复：全局后向剔除方法不需要额外参数
        # 后向剔除基于性能比较（HR和RMSE），不使用统计显著性阈值

        # 🔥 修复：EM算法参数设置
        col_left, col_right = st_instance.columns(2)

        with col_left:
            # 最大迭代次数
            if CONFIG_AVAILABLE:
                default_max_iter = TrainDefaults.EM_MAX_ITER
            else:
                default_max_iter = 30

            max_iter_value = st_instance.number_input(
                "最大迭代次数",
                min_value=10,
                max_value=1000,
                value=get_dfm_state('dfm_max_iter', default_max_iter),
                step=10,
                key='dfm_max_iter_input',
                help="EM算法的最大迭代次数"
            )
            set_dfm_state('dfm_max_iter', max_iter_value)

            # 因子自回归阶数
            if CONFIG_AVAILABLE:
                default_ar_order = TrainDefaults.FACTOR_AR_ORDER
            else:
                default_ar_order = 1

            ar_order_value = st_instance.number_input(
                "因子自回归阶数",
                min_value=0,
                max_value=5,
                value=get_dfm_state('dfm_factor_ar_order', default_ar_order),
                step=1,
                key='dfm_factor_ar_order_input',
                help="因子的自回归阶数，通常设为1"
            )
            set_dfm_state('dfm_factor_ar_order', ar_order_value)

        with col_right:
            # 预留空间，可以添加其他参数
            pass

    # --- 第三列: 因子选择策略 ---
    with col3_factor_specific:
        # 因子选择策略
        if CONFIG_AVAILABLE:
            factor_strategy_options = UIDefaults.FACTOR_SELECTION_STRATEGY_OPTIONS
            default_strategy = TrainDefaults.FACTOR_SELECTION_STRATEGY
        else:
            factor_strategy_options = {
                'information_criteria': "信息准则",
                'fixed_number': "固定因子数",
                'cumulative_variance': "累积方差贡献"
            }
            default_strategy = 'information_criteria'

        current_strategy = get_dfm_state('dfm_factor_selection_strategy', default_strategy)

        strategy_value = st_instance.selectbox(
            "因子选择策略",
            options=list(factor_strategy_options.keys()),
            format_func=lambda x: factor_strategy_options[x],
            index=list(factor_strategy_options.keys()).index(current_strategy),
            key='dfm_factor_selection_strategy_input',
            help="选择确定因子数量的方法"
        )
        set_dfm_state('dfm_factor_selection_strategy', strategy_value)

        # 根据策略显示相应参数
        if strategy_value == 'information_criteria':
            # 信息准则方法
            if CONFIG_AVAILABLE:
                ic_options = UIDefaults.INFORMATION_CRITERION_OPTIONS
                default_ic = TrainDefaults.INFORMATION_CRITERION
            else:
                ic_options = {
                    'bic': "BIC",
                    'aic': "AIC",
                    'hqc': "HQC"
                }
                default_ic = 'bic'

            current_ic = get_dfm_state('dfm_information_criterion', default_ic)

            ic_value = st_instance.selectbox(
                "信息准则方法",
                options=list(ic_options.keys()),
                format_func=lambda x: ic_options[x],
                index=list(ic_options.keys()).index(current_ic),
                key='dfm_information_criterion_input',
                help="选择信息准则类型"
            )
            set_dfm_state('dfm_information_criterion', ic_value)

            # IC最大因子数
            if CONFIG_AVAILABLE:
                default_ic_max = UIDefaults.IC_MAX_FACTORS_DEFAULT
            else:
                default_ic_max = 10

            ic_max_value = st_instance.number_input(
                "IC最大因子数",
                min_value=1,
                max_value=20,
                value=get_dfm_state('dfm_ic_max_factors', default_ic_max),
                step=1,
                key='dfm_ic_max_factors_input',
                help="信息准则搜索的最大因子数"
            )
            set_dfm_state('dfm_ic_max_factors', ic_max_value)

        elif strategy_value == 'fixed_number':
            # 固定因子数
            if CONFIG_AVAILABLE:
                default_fixed_factors = TrainDefaults.FIXED_NUMBER_OF_FACTORS
            else:
                default_fixed_factors = 3

            fixed_factors_value = st_instance.number_input(
                "固定因子数",
                min_value=1,
                max_value=15,
                value=get_dfm_state('dfm_fixed_number_of_factors', default_fixed_factors),
                step=1,
                key='dfm_fixed_number_of_factors_input',
                help="指定使用的因子数量"
            )
            set_dfm_state('dfm_fixed_number_of_factors', fixed_factors_value)

        elif strategy_value == 'cumulative_variance':
            # 累积方差贡献
            if CONFIG_AVAILABLE:
                default_cum_var = TrainDefaults.CUM_VARIANCE_THRESHOLD
            else:
                default_cum_var = 0.8

            cum_var_value = st_instance.number_input(
                "累积方差贡献阈值",
                min_value=0.5,
                max_value=0.99,
                value=get_dfm_state('dfm_cumulative_variance_threshold', default_cum_var),
                step=0.01,
                format="%.2f",
                key='dfm_cumulative_variance_threshold_input',
                help="因子累积解释方差的阈值"
            )
            set_dfm_state('dfm_cumulative_variance_threshold', cum_var_value)

    # --- 模型训练部分 ---
    st_instance.markdown("--- ")
    st_instance.subheader("🏋️ 模型训练")

    # 🔥 修复：检查训练准备状态
    current_target_var = get_dfm_state('dfm_target_variable', None)
    current_selected_indicators = get_dfm_state('dfm_selected_indicators', [])

    # 日期验证
    training_start_value = get_dfm_state('dfm_training_start_date')
    validation_start_value = get_dfm_state('dfm_validation_start_date')
    validation_end_value = get_dfm_state('dfm_validation_end_date')

    date_validation_passed = True
    if training_start_value and validation_start_value and validation_end_value:
        if training_start_value >= validation_start_value:
            st_instance.error("❌ 训练期开始日期必须早于验证期开始日期")
            date_validation_passed = False
        elif validation_start_value >= validation_end_value:
            st_instance.error("❌ 验证期开始日期必须早于验证期结束日期")
            date_validation_passed = False
        else:
            st_instance.success("✅ 日期设置验证通过")
    else:
        st_instance.warning("⚠️ 请设置完整的日期范围")
        date_validation_passed = False

    # 检查训练准备状态
    training_ready = (
        current_target_var is not None and
        len(current_selected_indicators) > 0 and
        date_validation_passed and
        input_df is not None
    )

    if not training_ready:
        st_instance.warning("⚠️ 训练条件未满足，请检查上述设置")

    # 训练按钮
    col_train_btn, col_reset_btn = st_instance.columns([1, 1])

    with col_train_btn:
        # 开始训练按钮
        if training_ready:
            if st_instance.button("🚀 开始训练",
                                key="dfm_start_training",
                                help="开始DFM模型训练",
                                use_container_width=True):



                # 🔥 修复：防止重复启动训练
                current_status = get_dfm_state('dfm_training_status', '等待开始')
                if current_status in ['正在训练...', '准备启动训练...']:
                    st_instance.warning("⚠️ 训练已在进行中，请勿重复启动")
                    return

                # 🔥 修复：使用真实的训练函数而不是模拟
                try:
                    # 🔥 关键修复：获取映射数据
                    var_type_map = get_dfm_state('dfm_var_type_map_obj', {})
                    var_industry_map = get_dfm_state('dfm_industry_map_obj', {})

                    # 调试信息
                    print(f"🔍 [映射数据传递] 类型映射: {len(var_type_map)} 个")
                    print(f"🔍 [映射数据传递] 行业映射: {len(var_industry_map)} 个")

                    # 准备训练参数（与train_and_save_dfm_results函数接口一致）
                    training_params = {
                        'training_data': input_df,  # 🔥 修复：参数名从 'input_df' 改为 'training_data' 以匹配训练组件期望
                        'target_variable': current_target_var,
                        'selected_indicators': current_selected_indicators,
                        'training_start_date': training_start_value,
                        'validation_start_date': validation_start_value,
                        'validation_end_date': validation_end_value,
                        'factor_selection_strategy': get_dfm_state('dfm_factor_selection_strategy', 'information_criteria'),
                        'max_iterations': get_dfm_state('dfm_max_iter', 100),
                        'max_lags': get_dfm_state('dfm_factor_ar_order', 1),  # 参数名修正
                        'variable_selection_method': get_dfm_state('dfm_variable_selection_method', 'none'),
                        # 🔥 关键修复：添加映射数据到训练参数
                        'var_type_map': var_type_map,
                        'var_industry_map': var_industry_map
                    }

                    # 根据因子选择策略添加相应参数
                    strategy = get_dfm_state('dfm_factor_selection_strategy', 'information_criteria')
                    if strategy == 'information_criteria':
                        training_params['info_criterion_method'] = get_dfm_state('dfm_information_criterion', 'bic')
                        training_params['ic_max_factors'] = get_dfm_state('dfm_ic_max_factors', 10)
                    elif strategy == 'fixed_number':
                        training_params['fixed_number_of_factors'] = get_dfm_state('dfm_fixed_number_of_factors', 3)
                    elif strategy == 'cumulative_variance':
                        training_params['cum_variance_threshold'] = get_dfm_state('dfm_cumulative_variance_threshold', 0.8)

                    # 如果使用全局后向剔除，添加相应参数
                    if get_dfm_state('dfm_variable_selection_method', 'none') == 'global_backward':
                        training_params['global_backward_threshold'] = get_dfm_state('dfm_global_backward_threshold', 0.05)

                    # 🔥 事件驱动：无需轮询计数器
                    
                    # 🔥 修复：使用组件化训练状态管理器启动训练
                    training_component = get_training_status_component()
                    success = training_component._start_training(training_params)

                    if success:
                        # 🔥 修复：设置训练状态为"正在训练..."
                        set_dfm_state('dfm_training_status', '正在训练...')
                        # 🔥 修复：添加训练启动标志，防止重复启动
                        set_dfm_state('dfm_training_started', True)

                        # 🔥 新增：启动自动刷新机制
                        st.session_state['auto_refresh_enabled'] = True
                        st.session_state['training_start_time'] = time.time()

                        # 🔥 修复：恢复页面刷新以显示训练状态变化
                        st_instance.rerun()
                    else:
                        st_instance.error("❌ 启动训练失败，请检查配置")

                except Exception as e:
                    st_instance.error(f"❌ 启动训练失败: {str(e)}")
        else:
            st_instance.button("🚀 开始训练",
                             disabled=True,
                             key="dfm_start_training_disabled",
                             help="请先满足所有训练条件",
                             use_container_width=True)

    with col_reset_btn:
        # 重置训练按钮
        if st_instance.button("🔄 重置训练",
                            key="dfm_reset_training_state",
                            help="重置所有训练状态",
                            use_container_width=True):
            # 🔥 修复：统一重置逻辑，彻底清理所有相关状态
            set_dfm_state('dfm_force_reset_training_state', True)
            _reset_training_state()
            # 🔥 修复：移除立即的成功消息和页面重新渲染，直接更新状态

    # 训练日志和结果显示（左右布局）
    col_log_left, col_result_right = st_instance.columns([2, 1])

    with col_log_left:
        st_instance.markdown("**训练日志**")

        # 🔥 修复：从持久化状态管理器读取日志
        training_log = get_dfm_state('dfm_training_log', [])
        current_training_status = get_dfm_state('dfm_training_status', '等待开始')

        # 🔥 修复：根据训练状态显示不同的日志信息
        if current_training_status == '正在训练...':
            if training_log:
                # 显示最近的日志条目
                log_text = "\n".join(training_log[-20:])  # 只显示最近20条
                st_instance.text_area(
                    "训练日志",
                    value=log_text,
                    height=300,
                    key="dfm_training_log_display",
                    help="显示最近20条训练日志",
                    label_visibility="hidden"
                )
                # 显示训练进度提示
                st_instance.info("🔄 训练正在进行中，日志实时更新...")
            else:
                st_instance.info("🔄 训练正在启动，请稍候...")
        elif training_log:
            # 显示最近的日志条目
            log_text = "\n".join(training_log[-20:])  # 只显示最近20条
            st_instance.text_area(
                "训练日志",
                value=log_text,
                height=300,
                key="dfm_training_log_display",
                help="显示最近20条训练日志",
                label_visibility="hidden"
            )
        else:
            st_instance.info("🔘 无日志")

    with col_result_right:
        st_instance.markdown("**文件下载**")

        # 🔥 修复：从持久化状态管理器读取状态，并添加验证
        training_status = get_dfm_state('dfm_training_status') or '等待开始'
        training_results = get_dfm_state('dfm_model_results_paths')
        
        # 🔥 新增：多重来源检查训练结果，确保能获取到文件路径
        if not training_results and training_status == '训练完成':
            # 尝试从session_state获取
            training_results = st_instance.session_state.get('dfm_model_results_paths')
            if training_results:
                debug_log("UI状态检查 - 从session_state获取到训练结果", "DEBUG")
                # 同步回状态管理器
                set_dfm_state('dfm_model_results_paths', training_results)

        # 🔥 新增：状态验证和调试信息（仅在调试模式下输出）
        from dashboard.ui.utils.debug_helpers import debug_log
        debug_log(f"UI状态检查 - 当前训练状态: {training_status}", "DEBUG")
        debug_log(f"UI状态检查 - 结果文件数量: {len(training_results) if training_results else 0}", "DEBUG")

        # 🔥 修复：优化状态处理逻辑，确保下载按钮正确显示
        if training_status == '正在训练...':
            st_instance.info("🔄 模型正在训练中，请耐心等待...")
            # 🔥 修复：训练中不显示下载按钮，只显示进度信息

        elif training_status == '训练完成':
            print(f"🔥 [UI状态检查] 检测到训练完成状态")
            # 🔥 事件驱动：训练完成后直接显示结果，无需轮询
            debug_training_state("训练完成，显示最终结果", show_in_ui=False)

            # 显示训练结果
            if training_results:
                st_instance.success("✅ 训练已完成")
                print(f"🔥 [UI状态检查] 开始处理训练结果，类型: {type(training_results)}")
                print(f"🔥 [UI状态检查] 训练结果内容: {training_results}")

                # 🔥 修复：正确处理字典格式的训练结果
                if isinstance(training_results, dict) and training_results:
                    st_instance.markdown("**生成的文件:**")
                    print(f"🔥 [UI状态检查] 处理字典格式结果，包含 {len(training_results)} 个条目")

                    # 显示文件信息
                    file_count = 0
                    available_files = []
                    for file_key, file_path in training_results.items():
                        print(f"🔥 [UI状态检查] 检查文件: {file_key} -> {file_path}")
                        if file_path and os.path.exists(file_path):
                            file_count += 1
                            file_name = os.path.basename(file_path)
                            file_size = _get_file_size(file_path)
                            st_instance.write(f"{file_count}. {file_name} ({file_size})")
                            available_files.append((file_key, file_path, file_name))
                            print(f"🔥 [UI状态检查] 文件存在: {file_name}")
                        else:
                            print(f"🔥 [UI状态检查] 文件不存在或路径为空: {file_path}")

                    if available_files:
                        st_instance.info(f"📁 共生成 {len(available_files)} 个文件")

                        # 为每个文件创建下载按钮
                        for file_key, file_path, file_name in available_files:
                            try:
                                # 读取文件数据
                                with open(file_path, 'rb') as f:
                                    file_data = f.read()

                                # 确定MIME类型和显示名称
                                if file_key == 'final_model_joblib':
                                    display_name = "📦 模型文件"
                                    mime_type = "application/octet-stream"
                                elif file_key == 'metadata':
                                    display_name = "📄 元数据文件"
                                    mime_type = "application/octet-stream"
                                elif file_key == 'excel_report':
                                    display_name = "📊 Excel报告"
                                    mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                else:
                                    display_name = f"📄 {file_key}"
                                    mime_type = "application/octet-stream"

                                # 创建下载按钮
                                st_instance.download_button(
                                    label=display_name,
                                    data=file_data,
                                    file_name=file_name,
                                    mime=mime_type,
                                    key=f"dfm_download_{file_key}",
                                    use_container_width=True
                                )

                            except Exception as e:
                                st_instance.warning(f"⚠️ {file_name} 文件读取失败: {e}")
                    else:
                        st_instance.warning("⚠️ 未找到可用的结果文件")

                elif isinstance(training_results, list) and training_results:
                    # 兼容旧的列表格式
                    st_instance.markdown("**生成的文件:**")
                    for i, file_path in enumerate(training_results, 1):
                        st_instance.write(f"{i}. {file_path}")
                    st_instance.info("📋 文件路径已显示，请手动复制")
                else:
                    st_instance.warning("⚠️ 训练完成但未找到结果文件")

        elif training_status.startswith('训练失败'):
            # 🔥 修复：处理训练失败状态
            training_error = get_dfm_state('dfm_training_error')
            st_instance.error(f"❌ {training_status}")
            if training_error:
                st_instance.error(f"错误详情: {training_error}")

        elif training_status == '等待开始':
            st_instance.info("🔘 无结果")

    # 🔥 事件驱动：在渲染结束时检查是否需要UI更新
    if ui_needs_update:
        logger.info("Event-driven UI update triggered")
        st_instance.rerun()


def render_dfm_model_training_page(st_module: Any) -> Dict[str, Any]:
    """
    渲染DFM模型训练页面

    Args:
        st_module: Streamlit模块

    Returns:
        Dict[str, Any]: 渲染结果
    """
    try:
        # 🔥 新增：页面开始时立即检查是否有待处理的UI更新
        training_ui_update_needed = st.session_state.get('training_ui_update_needed', False)
        force_ui_refresh = st.session_state.get('force_ui_refresh', False)

        if training_ui_update_needed or force_ui_refresh:
            print("🔥 [页面开始] 检测到待处理的UI更新，立即处理")
            # 清除标志
            st.session_state['training_ui_update_needed'] = False
            st.session_state['force_ui_refresh'] = False
            # 立即刷新
            st_module.rerun()
            return {}
        # 🔥 修复：直接调用主要的UI渲染函数，避免递归
        render_dfm_train_model_tab(st_module)

        return {
            'status': 'success',
            'page': 'model_training',
            'components': ['variable_selection', 'date_range', 'model_parameters', 'training_status']
        }

    except Exception as e:
        st_module.error(f"模型训练页面渲染失败: {str(e)}")
        return {
            'status': 'error',
            'page': 'model_training',
            'error': str(e)
        }


# 🔥 修复：移除重复的函数定义，避免递归调用
# 原来的render_dfm_train_model_tab函数已经在第122行定义，这里不需要重复定义

# --- 辅助函数 ---
def _get_file_size(file_path: str) -> str:
    """获取文件大小的可读格式"""
    try:
        size_bytes = os.path.getsize(file_path)
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    except Exception:
        return "未知大小"
