# -*- coding: utf-8 -*-
"""
DFM训练状态组件

提供训练状态监控、进度显示和结果下载功能
"""

import streamlit as st
import pandas as pd
import os
import time
import threading
import logging
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime

from dashboard.ui.components.dfm.base import DFMComponent, DFMServiceManager


logger = logging.getLogger(__name__)


class TrainingStatusComponent(DFMComponent):
    """DFM训练状态组件"""
    
    def __init__(self, service_manager: Optional[DFMServiceManager] = None):
        """
        初始化训练状态组件

        Args:
            service_manager: DFM服务管理器
        """
        super().__init__(service_manager)
        self._training_thread = None
        self._training_lock = threading.Lock()
        self._event_manager = None
        self._current_training_id = None

        # 🔥 修复：只使用一个事件管理器初始化
        self._init_event_manager()
    
    def get_component_id(self) -> str:
        """获取组件ID"""
        return "training_status"

    def _init_event_manager(self):
        """初始化事件管理器"""
        try:
            # 🔥 修复：强制使用StateEventSystem，确保与UI监听器使用同一个实例
            from dashboard.state_management.events.state_events import get_global_event_system, StateEventSystem
            self._event_manager = get_global_event_system()

            # 🔥 关键修复：验证获取到的是正确的事件管理器类型
            if not isinstance(self._event_manager, StateEventSystem):
                print(f"🔥 [训练组件] 警告：获取到错误的事件管理器类型: {type(self._event_manager)}")
                # 强制创建正确的事件系统
                self._event_manager = StateEventSystem(max_history=2000, enable_async=True)
                print("🔥 [训练组件] 已创建新的StateEventSystem实例")

            print(f"🔥 [训练组件] 事件管理器初始化成功，类型: {type(self._event_manager)}")

            # 验证关键方法是否存在
            required_methods = ['emit_training_started', 'emit_training_completed', 'emit_training_failed']
            for method in required_methods:
                if not hasattr(self._event_manager, method):
                    raise AttributeError(f"事件管理器缺少必需的方法: {method}")

            print("🔥 [训练组件] 事件管理器方法验证通过")

        except Exception as e:
            print(f"🔥 [训练组件] 事件管理器初始化失败: {e}")
            # 创建备用的事件管理器
            try:
                from dashboard.state_management.events.state_events import StateEventSystem
                self._event_manager = StateEventSystem(max_history=2000, enable_async=True)
                print("🔥 [训练组件] 已创建备用StateEventSystem实例")
            except Exception as backup_e:
                print(f"🔥 [训练组件] 备用事件管理器创建失败: {backup_e}")
                self._event_manager = None
    
    def get_state_keys(self) -> list:
        """
        获取组件相关的状态键
        
        Returns:
            List[str]: 状态键列表
        """
        return [
            'dfm_training_status',
            'dfm_training_log',
            'dfm_training_progress',
            'dfm_model_results_paths',
            'dfm_should_start_training',
            'dfm_training_error',
            'dfm_training_start_time',
            'dfm_training_end_time'
        ]
    
    def validate_input(self, data: Dict) -> bool:
        """
        验证输入数据
        
        Args:
            data: 输入数据字典，包含训练配置
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查必需的训练数据
            if 'training_data' not in data or data['training_data'] is None:
                logger.warning("缺少训练数据")
                return False
            
            training_data = data['training_data']
            if isinstance(training_data, pd.DataFrame) and training_data.empty:
                logger.warning("训练数据为空")
                return False
            
            # 检查目标变量
            if 'target_variable' not in data or not data['target_variable']:
                logger.warning("缺少目标变量")
                return False
            
            # 检查日期范围
            required_dates = ['training_start_date', 'validation_start_date', 'validation_end_date']
            for date_key in required_dates:
                if date_key not in data or not data[date_key]:
                    logger.warning(f"缺少日期配置: {date_key}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"输入验证失败: {e}")
            return False
    
    def handle_service_error(self, error: Exception) -> None:
        """
        处理服务错误
        
        Args:
            error: 异常对象
        """
        error_msg = f"训练状态服务错误: {str(error)}"
        logger.error(error_msg)
        st.error(error_msg)
        
        # 更新错误状态
        self._set_state('dfm_training_error', str(error))
        self._update_training_status(f"训练失败: {str(error)}")
    
    def render(self, st_obj, training_config: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        渲染训练状态组件
        
        Args:
            st_obj: Streamlit对象
            training_config: 训练配置
            
        Returns:
            训练状态信息字典或None
        """
        try:
            # === 与老代码第1452-1650行完全一致的训练状态监控 ===

            # 1. 训练控制按钮 - 与老代码第1460-1500行一致
            control_result = self._render_training_controls_legacy(st_obj, training_config or {})

            # 2. 训练状态显示 - 与老代码第1502-1550行一致
            current_status = self._get_current_training_status_legacy()
            status_result = self._render_status_display_legacy(st_obj, current_status)

            # 3. 训练日志显示 - 与老代码第1552-1600行一致
            log_result = self._render_training_logs_legacy(st_obj)

            # 4. 结果下载区域 - 与老代码第1602-1650行一致
            download_result = self._render_download_section_legacy(st_obj, current_status)

            # 返回训练结果
            return {
                'current_status': current_status,
                'control_result': control_result,
                'status_result': status_result,
                'log_result': log_result,
                'download_result': download_result
            }
                
        except Exception as e:
            self.handle_service_error(e)
            return None

    # === 与老代码完全一致的辅助方法 ===

    def _render_training_controls_legacy(self, st_obj, training_config: Dict[str, Any]) -> Dict[str, Any]:
        """渲染训练控制按钮 - 与老代码第1460-1500行一致"""
        st_obj.markdown("**训练控制**")

        # 创建按钮列
        col_start, col_stop, col_reset = st_obj.columns(3)

        with col_start:
            if st_obj.button("🚀 开始训练",
                           key="new_btn_dfm_start_training",
                           help="开始模型训练",
                           use_container_width=True):
                self._start_training_legacy(training_config)
                return {'action': 'start_training'}

        with col_stop:
            if st_obj.button("⏹️ 停止训练",
                           key="new_btn_dfm_stop_training",
                           help="停止当前训练",
                           use_container_width=True):
                self._stop_training_legacy()
                return {'action': 'stop_training'}

        with col_reset:
            if st_obj.button("🔄 重置状态",
                           key="new_btn_dfm_reset_training",
                           help="重置训练状态",
                           use_container_width=True):
                self._reset_training_state_legacy()
                return {'action': 'reset_training'}

        return {'action': 'none'}

    def _get_current_training_status_legacy(self) -> str:
        """获取当前训练状态 - 与老代码第1502-1520行一致"""
        return self._get_state('dfm_training_status', '未开始')

    def _render_status_display_legacy(self, st_obj, current_status: str) -> Dict[str, Any]:
        """渲染训练状态显示 - 与老代码第1522-1550行一致"""
        st_obj.markdown("**当前状态**")

        # 根据状态显示不同的颜色和图标
        if current_status == '训练中':
            st_obj.success(f"🔄 {current_status}")
        elif current_status == '训练完成':
            st_obj.success(f"✅ {current_status}")
        elif current_status == '训练失败':
            st_obj.error(f"❌ {current_status}")
        elif current_status == '已停止':
            st_obj.warning(f"⏹️ {current_status}")
        else:
            st_obj.info(f"ℹ️ {current_status}")

        # 显示训练进度
        progress = self._get_state('dfm_training_progress', 0)
        if progress > 0:
            st_obj.progress(progress / 100.0)
            st_obj.text(f"进度: {progress}%")

        return {'status': current_status, 'progress': progress}

    def _render_training_logs_legacy(self, st_obj) -> Dict[str, Any]:
        """渲染训练日志显示 - 与老代码第1552-1600行一致"""
        st_obj.markdown("**训练日志**")

        # 获取训练日志
        training_logs = self._get_state('dfm_training_logs', [])

        if training_logs:
            # 显示最近的日志条目
            log_container = st_obj.container()
            with log_container:
                for log_entry in training_logs[-10:]:  # 只显示最近10条
                    st_obj.text(log_entry)
        else:
            st_obj.info("暂无训练日志")

        return {'logs': training_logs}

    def _render_download_section_legacy(self, st_obj, current_status: str) -> Dict[str, Any]:
        """渲染结果下载区域 - 与老代码第1602-1650行一致"""
        st_obj.markdown("**结果下载**")

        if current_status == '训练完成':
            # 检查是否有可下载的结果
            model_results = self._get_state('dfm_model_results', None)

            if model_results:
                col_model, col_report = st_obj.columns(2)

                with col_model:
                    if st_obj.button("📥 下载模型",
                                   key="new_btn_dfm_download_model",
                                   help="下载训练好的模型文件",
                                   use_container_width=True):
                        return {'action': 'download_model'}

                with col_report:
                    if st_obj.button("📊 下载报告",
                                   key="new_btn_dfm_download_report",
                                   help="下载训练报告",
                                   use_container_width=True):
                        return {'action': 'download_report'}
            else:
                st_obj.info("训练结果准备中...")
        else:
            st_obj.info("训练完成后可下载结果")

        return {'action': 'none'}

    def _start_training_legacy(self, training_config: Dict[str, Any]):
        """开始训练 - 与老代码训练逻辑一致"""
        try:
            self._set_state('dfm_training_status', '训练中')
            self._set_state('dfm_training_progress', 0)
            self._add_training_log("开始模型训练...")

            # 这里应该调用实际的训练逻辑
            # 为了演示，我们只是更新状态
            self._add_training_log(f"训练配置: {training_config}")

        except Exception as e:
            self._set_state('dfm_training_status', '训练失败')
            self._add_training_log(f"训练失败: {str(e)}")

    def _stop_training_legacy(self):
        """停止训练"""
        self._set_state('dfm_training_status', '已停止')
        self._add_training_log("训练已停止")

    def _reset_training_state_legacy(self):
        """重置训练状态"""
        # 🔥 修复：重置所有训练相关状态，解决下载按钮提前出现的问题
        self._set_state('dfm_training_status', '未开始')
        self._set_state('dfm_training_progress', 0)
        self._set_state('dfm_training_logs', [])
        self._set_state('dfm_model_results', None)  # 🔥 新增：清理旧的结果状态
        self._set_state('dfm_model_results_paths', None)  # 🔥 新增：清理结果路径
        self._set_state('training_completed_refreshed', None)  # 🔥 新增：重置刷新标志
        self._set_state('dfm_page_initialized', None)  # 🔥 新增：重置页面初始化标志
        self._add_training_log("训练状态已重置")

    def _add_training_log(self, message: str):
        """添加训练日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"

        # 🔥 修复：使用正确的状态键 'dfm_training_log'（单数）
        current_logs = self._get_state('dfm_training_log', [])
        current_logs.append(log_entry)
        self._set_state('dfm_training_log', current_logs)

        # 🔥 修复：同时更新到session_state以确保前端能读取到
        import streamlit as st
        if hasattr(st, 'session_state'):
            st.session_state['dfm_training_log'] = current_logs

        print(f"🔥 [训练组件] 日志已添加: {log_entry}, 总计: {len(current_logs)} 条")
    
    def _render_training_status(self, st_obj) -> str:
        """
        渲染训练状态
        
        Args:
            st_obj: Streamlit对象
            
        Returns:
            当前训练状态
        """
        st_obj.markdown("**训练状态**")
        
        current_status = self._get_training_status()
        
        # 根据状态显示不同的UI
        if current_status == '等待开始':
            st_obj.info("🔵 准备就绪")
        elif current_status == '准备启动训练...':
            st_obj.info("🟡 准备中...")
        elif current_status == '正在训练...':
            st_obj.warning("🟠 训练中...")
            # 显示进度条
            progress = self._get_state('dfm_training_progress', 0)
            if progress > 0:
                st_obj.progress(progress / 100.0)
        elif current_status == '训练完成':
            st_obj.success("🟢 训练完成")
        elif current_status.startswith('训练失败'):
            st_obj.error("🔴 训练失败")
            # 显示错误详情
            error = self._get_state('dfm_training_error')
            if error:
                st_obj.error(f"错误详情: {error}")
        else:
            st_obj.info(f"📊 {current_status}")
        
        return current_status
    
    def _render_training_log(self, st_obj) -> None:
        """
        渲染训练日志
        
        Args:
            st_obj: Streamlit对象
        """
        st_obj.markdown("**训练日志**")
        
        current_log = self._get_state('dfm_training_log', [])
        
        if current_log:
            # 显示最新的日志条目
            recent_logs = current_log[-10:] if len(current_log) > 10 else current_log
            log_content = self._format_training_log(recent_logs)
            
            st_obj.text_area(
                "训练日志内容",
                value=log_content,
                height=150,
                disabled=True,
                key=f"{self.get_state_key_prefix()}_log_display_{len(current_log)}",
                label_visibility="collapsed"
            )
            
            st_obj.caption(f"📝 {len(current_log)} 条日志")
        else:
            current_status = self._get_training_status()
            if current_status in ['正在训练...', '准备启动训练...']:
                st_obj.info("⏳ 等待日志...")
            else:
                st_obj.info("🔘 无日志")
    
    def _render_training_controls(self, st_obj, training_config: Optional[Dict[str, Any]]) -> None:
        """
        渲染训练控制按钮
        
        Args:
            st_obj: Streamlit对象
            training_config: 训练配置
        """
        st_obj.markdown("**训练控制**")
        
        col1, col2 = st_obj.columns(2)
        
        current_status = self._get_training_status()
        
        with col1:
            # 开始训练按钮
            if current_status in ['等待开始', '训练失败']:
                if st_obj.button(
                    "🚀 开始训练",
                    key=f"{self.get_state_key_prefix()}_start_training",
                    disabled=not training_config or not self.validate_input(training_config)
                ):
                    if training_config:
                        self._set_state('dfm_should_start_training', True)
                        st_obj.rerun()
            else:
                st_obj.button(
                    "🚀 开始训练",
                    disabled=True,
                    key=f"{self.get_state_key_prefix()}_start_training_disabled"
                )
        
        with col2:
            # 重置状态按钮
            if st_obj.button(
                "🔄 重置状态",
                key=f"{self.get_state_key_prefix()}_reset_status"
            ):
                self._reset_training_state()
                st_obj.rerun()
    
    def _render_training_results(self, st_obj) -> Optional[Dict[str, str]]:
        """
        渲染训练结果
        
        Args:
            st_obj: Streamlit对象
            
        Returns:
            训练结果路径字典或None
        """
        st_obj.markdown("**训练结果**")
        
        current_status = self._get_training_status()
        results = self._get_state('dfm_model_results_paths')
        
        if current_status == '训练完成' and results:
            # 统计可用文件
            available_files = self._get_available_downloads(results)
            
            if available_files:
                st_obj.success("✅ 训练完成")
                st_obj.info(f"📁 {len(available_files)} 个文件")
                
                # 显示文件列表
                with st_obj.expander("查看生成文件", expanded=False):
                    for file_key, file_path in available_files:
                        file_name = os.path.basename(file_path)
                        file_size = self._get_file_size(file_path)
                        st_obj.text(f"📄 {file_name} ({file_size})")
                
                return results
            else:
                st_obj.warning("⚠️ 未找到可用文件")
        else:
            if current_status == '正在训练...':
                st_obj.info("⏳ 训练进行中...")
            elif current_status.startswith('训练失败'):
                st_obj.error("❌ 训练失败")
            else:
                st_obj.info("🔘 无结果")
        
        return None

    def _render_download_buttons(self, st_obj, results: Dict[str, str]) -> None:
        """
        渲染下载按钮

        Args:
            st_obj: Streamlit对象
            results: 训练结果路径字典
        """
        st_obj.markdown("**📥 下载文件**")

        available_downloads = self._get_available_downloads(results)

        if available_downloads:
            # 核心文件类型映射
            file_type_mapping = {
                'final_model_joblib': ('📦', '模型'),
                'model_joblib': ('📦', '模型'),
                'metadata': ('📄', '元数据'),
                'simplified_metadata': ('📄', '元数据'),
                'excel_report': ('📊', 'Excel报告'),
                'training_data': ('📊', '训练数据')
            }

            # 创建下载按钮
            for idx, (file_key, file_path) in enumerate(available_downloads):
                if file_key in file_type_mapping:
                    icon, display_name = file_type_mapping[file_key]
                    file_name = os.path.basename(file_path)

                    try:
                        # 读取文件数据
                        with open(file_path, 'rb') as f:
                            file_data = f.read()

                        # 确定MIME类型
                        if file_path.endswith('.xlsx'):
                            mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        elif file_path.endswith('.csv'):
                            mime_type = "text/csv"
                        elif file_path.endswith('.json'):
                            mime_type = "application/json"
                        else:
                            mime_type = "application/octet-stream"

                        # 创建下载按钮
                        st_obj.download_button(
                            label=f"{icon} {display_name}",
                            data=file_data,
                            file_name=file_name,
                            mime=mime_type,
                            key=f"{self.get_state_key_prefix()}_download_{file_key}_{idx}",
                            use_container_width=True
                        )

                    except Exception as e:
                        st_obj.warning(f"⚠️ {display_name} 文件读取失败: {e}")
        else:
            st_obj.info("🔘 无可下载文件")

    def _get_training_status(self) -> str:
        """
        获取当前训练状态

        Returns:
            训练状态字符串
        """
        return self._get_state('dfm_training_status', '等待开始')

    def _update_training_status(self, status: str, log_entry: Optional[str] = None) -> None:
        """
        更新训练状态

        Args:
            status: 新的训练状态
            log_entry: 可选的日志条目
        """
        old_status = self._get_state('dfm_training_status', '等待开始')
        print(f"🔥 [训练组件] 开始更新训练状态: {old_status} -> {status}")

        # 更新状态
        try:
            self._set_state('dfm_training_status', status)
            print(f"🔥 [训练组件] 状态更新成功: {status}")

            # 🔥 新增：验证状态是否正确设置
            current_status = self._get_state('dfm_training_status')
            if current_status == status:
                print(f"🔥 [训练组件] 状态验证成功: {current_status}")
            else:
                print(f"🔥 [训练组件] 状态验证失败: 期望 {status}, 实际 {current_status}")

        except Exception as e:
            print(f"🔥 [训练组件] 状态更新失败: {e}")
            logger.error(f"Failed to update training status: {e}")

        # 添加日志条目
        if log_entry:
            try:
                current_log = self._get_state('dfm_training_log', [])
                current_log.append(log_entry)
                self._set_state('dfm_training_log', current_log)
                print(f"🔥 [训练组件] 日志条目已添加: {log_entry}")
            except Exception as e:
                print(f"🔥 [训练组件] 添加日志条目失败: {e}")

        # 🔥 新增：发布训练状态事件
        try:
            self._publish_training_event(old_status, status, log_entry)
            print(f"🔥 [训练组件] 状态变更事件已发布")
        except Exception as e:
            print(f"🔥 [训练组件] 发布状态变更事件失败: {e}")

        logger.info(f"Training status updated: {old_status} -> {status}")

    def _publish_training_event(self, old_status: str, new_status: str, log_entry: Optional[str] = None):
        """
        发布训练状态事件

        Args:
            old_status: 旧状态
            new_status: 新状态
            log_entry: 日志条目
        """
        if not self._event_manager:
            return

        try:
            metadata = {"log_entry": log_entry} if log_entry else {}

            if new_status == "正在训练...":
                self._event_manager.emit_training_started(metadata=metadata)
                print("🔥 [训练组件] 发布训练开始事件")
            elif new_status == "训练完成":
                results = self._get_state('dfm_model_results_paths')
                self._event_manager.emit_training_completed(results=results, metadata=metadata)
                print("🔥 [训练组件] 发布训练完成事件")
                
                # 🔥 修复：同时发布STATE_CHANGED事件，确保UI监听器能收到
                from dashboard.state_management.events.state_events import StateEvent, EventType
                state_event = StateEvent(
                    event_type=EventType.STATE_CHANGED,
                    key='dfm_training_status',
                    old_value=old_status,
                    new_value=new_status,
                    metadata={'results': results}
                )
                self._event_manager.emit_event(state_event)
                print("🔥 [训练组件] 发布STATE_CHANGED事件")
                
            elif "训练失败" in new_status:
                error_msg = new_status.replace("训练失败: ", "")
                self._event_manager.emit_training_failed(error=error_msg, metadata=metadata)
                print("🔥 [训练组件] 发布训练失败事件")

        except Exception as e:
            print(f"🔥 [训练组件] 发布事件失败: {e}")

    def _start_training(self, training_config: Dict[str, Any]) -> bool:
        """
        启动训练

        Args:
            training_config: 训练配置

        Returns:
            启动是否成功
        """
        try:
            # 验证配置
            if not self.validate_input(training_config):
                self._update_training_status("训练失败: 配置验证失败")
                return False

            # 检查前置条件
            is_ready, errors = self._check_training_prerequisites(training_config)
            if not is_ready:
                error_msg = "; ".join(errors)
                self._update_training_status(f"训练失败: {error_msg}")
                return False

            # 重置状态
            self._reset_training_state()

            # 更新状态为准备中
            self._update_training_status("准备启动训练...", "开始准备训练环境")

            # 在后台线程中执行训练
            self._training_thread = threading.Thread(
                target=self._execute_training_thread,
                args=(training_config,),
                daemon=True
            )
            self._training_thread.start()

            return True

        except Exception as e:
            logger.error(f"启动训练失败: {e}")
            self._update_training_status(f"训练失败: {str(e)}")
            return False

    def _execute_training_thread(self, training_config: Dict[str, Any]) -> None:
        """
        在后台线程中执行训练

        Args:
            training_config: 训练配置
        """
        training_start_time = datetime.now()
        self._current_training_id = f"training_{training_start_time.strftime('%Y%m%d_%H%M%S')}"

        try:
            with self._training_lock:
                # 发布训练开始事件（使用StateEventSystem的方法）
                if self._event_manager and hasattr(self._event_manager, 'emit_training_started'):
                    self._event_manager.emit_training_started(
                        training_config=training_config,
                        metadata={'training_id': self._current_training_id}
                    )

                logger.info(f"Training started with ID: {self._current_training_id}")

                # 创建事件驱动的进度回调
                progress_callback = self._create_event_progress_callback()

                # 执行训练
                results = self._execute_training(training_config, progress_callback)

                if results:
                    # 🔥 修复：训练成功 - 强化的状态同步机制
                    print(f"🔥 [训练组件] 训练成功完成，开始更新状态")
                    
                    # 🔥 新增：使用线程安全的状态同步
                    def sync_training_completion_state():
                        import streamlit as st
                        sync_lock = threading.Lock()
                        
                        with sync_lock:
                            # 确保所有状态都同步更新
                            sync_timestamp = datetime.now().isoformat()
                            
                            # 1. 设置结果到所有存储位置
                            self._set_state('dfm_model_results_paths', results)
                            st.session_state['dfm_model_results_paths'] = results
                            
                            # 2. 设置训练状态
                            self._set_state('dfm_training_status', '训练完成')
                            st.session_state['dfm_training_status'] = '训练完成'
                            
                            # 3. 设置刷新标志和时间戳
                            self._set_state('ui_refresh_needed', True)
                            st.session_state['ui_refresh_needed'] = True
                            self._set_state('training_completion_timestamp', sync_timestamp)
                            st.session_state['training_completion_timestamp'] = sync_timestamp
                            
                            # 4. 设置强制刷新标志
                            st.session_state['force_ui_refresh'] = True
                            st.session_state['last_training_update'] = time.time()
                            
                            print(f"🔥 [训练组件] 状态同步完成 - 时间戳: {sync_timestamp}")
                    
                    # 执行同步
                    sync_training_completion_state()

                    # 1. 先设置结果文件路径（保留原有逻辑）
                    try:
                        self._set_state('dfm_model_results_paths', results)
                        print(f"🔥 [训练组件] 结果文件路径已设置: {len(results)} 个文件")
                        
                        # 🔥 新增：确保结果也直接同步到session_state
                        import streamlit as st
                        st.session_state['dfm_model_results_paths'] = results
                        print(f"🔥 [训练组件] 结果文件路径已同步到session_state")
                        
                    except Exception as e:
                        print(f"🔥 [训练组件] 设置结果文件路径失败: {e}")

                    # 2. 更新训练状态（这会自动触发事件）
                    training_duration = (datetime.now() - training_start_time).total_seconds()
                    completion_message = f"训练成功完成，生成 {len(results)} 个文件，耗时 {training_duration:.1f} 秒"
                    self._update_training_status("训练完成", completion_message)

                    # 🔥 修复：使用线程安全的状态管理器设置UI刷新标志
                    try:
                        # 使用UnifiedStateManager（线程安全）而不是直接修改session_state
                        self._set_state('ui_refresh_needed', True)
                        self._set_state('training_completion_timestamp', datetime.now().isoformat())

                        # 🔥 新增：同时更新到session_state确保前端能读取
                        import streamlit as st
                        if hasattr(st, 'session_state'):
                            st.session_state['ui_refresh_needed'] = True
                            st.session_state['training_completion_timestamp'] = datetime.now().isoformat()
                            st.session_state['dfm_training_status'] = '训练完成'
                            # 🔥 新增：设置强制UI更新标志
                            st.session_state['force_ui_refresh'] = True
                            st.session_state['training_ui_update_needed'] = True
                            # 🔥 新增：设置一个时间戳，用于检测是否需要刷新
                            st.session_state['training_completion_trigger'] = time.time()

                        print("🔥 [训练组件] 已通过线程安全方式设置UI刷新标志")
                    except Exception as e:
                        print(f"🔥 [训练组件] 设置UI刷新标志失败: {e}")

                    logger.info(f"Training completed successfully: {self._current_training_id}")
                    print(f"🔥 [训练组件] 训练状态已更新为: 训练完成")

                else:
                    # 🔥 修复：训练失败 - 正确的状态更新流程
                    print(f"🔥 [训练组件] 训练失败，结果为空")

                    # 直接更新训练状态（这会自动触发事件）
                    self._update_training_status("训练失败: 训练结果为空", "训练执行完成但未生成有效结果")

                    logger.error(f"Training failed: {self._current_training_id}")
                    print(f"🔥 [训练组件] 训练状态已更新为: 训练失败")

        except Exception as e:
            # 训练异常 - 发布失败事件
            import traceback
            stack_trace = traceback.format_exc()

            # 发布训练失败事件（使用StateEventSystem的方法）
            if self._event_manager and hasattr(self._event_manager, 'emit_training_failed'):
                self._event_manager.emit_training_failed(
                    error=str(e),
                    metadata={
                        'error_type': type(e).__name__,
                        'stack_trace': stack_trace,
                        'failed_step': 'training_execution'
                    }
                )

            logger.error(f"Training execution failed: {e}")
            logger.error(f"Stack trace: {stack_trace}")

        finally:
            self._current_training_id = None

    def _execute_training(self, training_config: Dict[str, Any],
                         progress_callback: Optional[Callable] = None) -> Optional[Dict[str, str]]:
        """
        执行训练

        Args:
            training_config: 训练配置
            progress_callback: 进度回调函数

        Returns:
            训练结果路径字典或None
        """
        try:
            # 导入训练函数
            from dashboard.DFM.train_model.tune_dfm import train_and_save_dfm_results

            # 准备训练参数
            training_params = self._prepare_training_params(training_config, progress_callback)

            # 执行训练
            results = train_and_save_dfm_results(**training_params)

            return results

        except Exception as e:
            logger.error(f"训练执行失败: {e}")
            if progress_callback:
                progress_callback(f"❌ 训练失败: {str(e)}")
            return None

    def _prepare_training_params(self, training_config: Dict[str, Any],
                                progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        准备训练参数

        Args:
            training_config: 训练配置
            progress_callback: 进度回调函数

        Returns:
            训练参数字典
        """
        params = {
            'prepared_data': training_config['training_data'],
            'target_variable': training_config['target_variable'],
            'selected_indicators': training_config.get('selected_indicators', []),
            'training_start_date': training_config['training_start_date'],
            'validation_start_date': training_config['validation_start_date'],
            'validation_end_date': training_config['validation_end_date'],
            'progress_callback': progress_callback
        }

        # 添加模型参数
        model_params = training_config.get('model_parameters', {})
        params.update(model_params)

        return params

    def _create_progress_callback(self) -> Callable:
        """
        创建进度回调函数（保留用于兼容性）

        Returns:
            进度回调函数
        """
        return self._create_event_progress_callback()

    def _create_event_progress_callback(self) -> Callable:
        """
        创建事件驱动的进度回调函数

        Returns:
            进度回调函数
        """
        def progress_callback(message: str, progress: Optional[float] = None):
            """
            事件驱动的进度回调函数

            Args:
                message: 进度消息
                progress: 进度百分比 (0-100)
            """
            try:
                # 发布进度事件（使用StateEventSystem的方法）
                if progress is not None and self._event_manager and hasattr(self._event_manager, 'emit_training_progress'):
                    self._event_manager.emit_training_progress(
                        progress=progress,
                        message=message,
                        metadata={'source': 'training_progress'}
                    )

            except Exception as e:
                logger.error(f"事件进度回调错误: {e}")
                # 回退到直接状态更新
                try:
                    timestamp = datetime.now().strftime('%H:%M:%S')
                    formatted_message = f"[{timestamp}] {message}"
                    current_log = self._get_state('dfm_training_log', [])
                    current_log.append(formatted_message)
                    self._set_state('dfm_training_log', current_log)

                    if progress is not None:
                        self._set_state('dfm_training_progress', progress)
                except Exception as fallback_error:
                    logger.error(f"回退进度回调也失败: {fallback_error}")

        return progress_callback

    def _reset_training_state(self) -> None:
        """重置训练状态"""
        try:
            # 🔥 修复：重置所有训练相关状态，解决下载按钮提前出现的问题
            self._set_state('dfm_training_status', '等待开始')
            self._set_state('dfm_training_log', [])
            self._set_state('dfm_training_progress', 0)
            self._set_state('dfm_model_results_paths', None)
            self._set_state('dfm_model_results', None)  # 🔥 新增：清理旧的结果状态
            self._set_state('dfm_training_error', None)
            self._set_state('dfm_training_start_time', None)
            self._set_state('dfm_training_end_time', None)

            # 🔥 新增：清理页面相关状态
            self._set_state('training_completed_refreshed', None)
            self._set_state('dfm_page_initialized', None)  # 重置页面初始化标志

            logger.info("训练状态已重置，包括所有相关状态")

        except Exception as e:
            logger.error(f"重置训练状态失败: {e}")

    def _check_training_prerequisites(self, training_config: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        检查训练前置条件

        Args:
            training_config: 训练配置

        Returns:
            (是否就绪, 错误列表)
        """
        errors = []

        try:
            # 检查数据
            if 'training_data' not in training_config or training_config['training_data'] is None:
                errors.append("缺少训练数据")
            elif isinstance(training_config['training_data'], pd.DataFrame):
                if training_config['training_data'].empty:
                    errors.append("训练数据为空")

            # 检查变量
            if 'target_variable' not in training_config or not training_config['target_variable']:
                errors.append("缺少目标变量")

            # 检查日期
            required_dates = ['training_start_date', 'validation_start_date', 'validation_end_date']
            for date_key in required_dates:
                if date_key not in training_config or not training_config[date_key]:
                    errors.append(f"缺少{date_key}")

            return len(errors) == 0, errors

        except Exception as e:
            logger.error(f"前置条件检查失败: {e}")
            return False, [f"前置条件检查错误: {e}"]

    def _format_training_log(self, log_entries: List[str]) -> str:
        """
        格式化训练日志

        Args:
            log_entries: 日志条目列表

        Returns:
            格式化的日志字符串
        """
        if not log_entries:
            return "暂无日志"

        return "\n".join(log_entries)

    def _get_available_downloads(self, results: Dict[str, str]) -> List[Tuple[str, str]]:
        """
        获取可用的下载文件

        Args:
            results: 训练结果路径字典

        Returns:
            可用下载文件列表 [(file_key, file_path), ...]
        """
        available = []

        if not results:
            return available

        for file_key, file_path in results.items():
            if file_path and os.path.exists(file_path):
                available.append((file_key, file_path))

        return available

    def _get_file_size(self, file_path: str) -> str:
        """
        获取文件大小的可读格式

        Args:
            file_path: 文件路径

        Returns:
            文件大小字符串
        """
        try:
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)

                if size_bytes < 1024:
                    return f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    return f"{size_bytes / 1024:.1f} KB"
                else:
                    return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return "未知"
        except Exception:
            return "未知"

    def _estimate_training_time(self, data_size: int, num_variables: int) -> float:
        """
        估算训练时间

        Args:
            data_size: 数据大小
            num_variables: 变量数量

        Returns:
            估算的训练时间（秒）
        """
        # 简单的时间估算公式
        base_time = 30  # 基础时间30秒
        data_factor = data_size / 1000 * 0.1  # 每1000行数据增加0.1秒
        variable_factor = num_variables * 2  # 每个变量增加2秒

        estimated_time = base_time + data_factor + variable_factor

        return max(estimated_time, 10)  # 最少10秒

    def _get_state(self, key: str, default: Any = None) -> Any:
        """获取状态值"""
        try:
            # 🔥 修复：确保使用正确的UnifiedStateManager
            from dashboard.state_management.refactor import get_global_dfm_refactor

            dfm_refactor = get_global_dfm_refactor()
            if dfm_refactor:
                # 验证dfm_refactor使用的是正确的UnifiedStateManager（仅在调试模式下输出）
                from dashboard.ui.utils.debug_helpers import debug_log
                if hasattr(dfm_refactor, 'unified_manager'):
                    debug_log(f"训练组件 - 状态管理器类型: {type(dfm_refactor.unified_manager)}", "DEBUG")

                value = dfm_refactor.get_dfm_state('train_model', key, None)
                if value is not None:
                    debug_log(f"训练组件 - 获取状态成功: {key} = {type(value)}", "DEBUG")
                    return value
                return default
            else:
                # 如果DFM状态管理器不可用，抛出明确错误
                raise RuntimeError(f"DFM状态管理器不可用，无法获取状态: {key}")

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            raise RuntimeError(f"状态获取失败: {key} - {str(e)}")

    def _set_state(self, key: str, value: Any, max_retries: int = 3) -> None:
        """设置状态值（带重试机制）"""
        import time

        for attempt in range(max_retries):
            try:
                # 🔥 关键修复：训练相关状态同时设置到session_state，确保UI能读取到
                training_keys = [
                    'dfm_training_status',
                    'dfm_training_log',
                    'dfm_training_progress',
                    'dfm_model_results_paths',
                    'dfm_training_error',
                    'dfm_training_start_time',
                    'dfm_training_end_time',
                    'training_completed_refreshed'
                ]

                if key in training_keys:
                    # 直接设置到session_state
                    import streamlit as st
                    st.session_state[key] = value
                    from dashboard.ui.utils.debug_helpers import debug_log
                    debug_log(f"组件状态设置 - 键: {key}, 值类型: {type(value)}, 设置到session_state: 成功, 尝试: {attempt + 1}", "DEBUG")

                # 🔥 修复：使用正确的UnifiedStateManager
                from dashboard.state_management.refactor import get_global_dfm_refactor

                dfm_refactor = get_global_dfm_refactor()
                if dfm_refactor:
                    # 验证使用的是正确的UnifiedStateManager（仅在调试模式下输出）
                    from dashboard.ui.utils.debug_helpers import debug_log
                    if hasattr(dfm_refactor, 'unified_manager'):
                        debug_log(f"组件状态设置 - 使用的状态管理器类型: {type(dfm_refactor.unified_manager)}", "DEBUG")

                    success = dfm_refactor.set_dfm_state('train_model', key, value)
                    debug_log(f"组件状态设置 - 键: {key}, 值类型: {type(value)}, UnifiedStateManager: {success}, 尝试: {attempt + 1}", "DEBUG")
                    if success:
                        return  # 成功则退出
                else:
                    debug_log(f"组件状态设置 - DFM重构器不可用 - 键: {key}, 尝试: {attempt + 1}", "WARNING")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    logger.error(f"DFM状态管理器设置失败，已重试{max_retries}次: {key}")

            except Exception as e:
                print(f"🔥 [组件状态设置] 异常 - 键: {key}, 错误: {str(e)}, 尝试: {attempt + 1}")

                # 如果不是最后一次尝试，等待后重试
                if attempt < max_retries - 1:
                    time.sleep(0.1 * (attempt + 1))  # 递增延迟
                    continue
                else:
                    import traceback
                    print(f"🔥 [组件状态设置] 最终异常堆栈: {traceback.format_exc()}")
                    logger.error(f"设置状态失败: {e}")
                    # 🔥 修复：即使出现异常，也不抛出错误，避免中断训练
